CREATE TABLE `user`
(
    `id`            bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`    bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`   bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`    bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`   bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`    bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `username`      varchar(128)   not null default '' comment '用户名(邮箱)',
    `password`      varchar(256)   not null default '' comment '密码',
    `code`          varchar(32)    not null default '' comment '用户的唯一码',
    `balance`       decimal(20, 4) not null default 0.0000 comment '账户余额',
    `register_time` bigint         not null default 0 comment '注册时间',
    `site` tinyint not null  default 1 comment '站点 1-国际站 2-国内站',
    key idx_username (username),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户表';


CREATE TABLE `user_balance_log`
(
    `id`          bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`  bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid` bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`  bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid` bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`      bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`  bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`     bigint         not null default 0 comment '用户ID',
    `type`        tinyint(1)     not null default 0 comment '余额类型 1-充值 2-消费 3-退款',
    `amount`      decimal(20, 4) not null default 0.0000 comment '金额',
    `trade_id`    bigint(20)     NOT NULL default 0 COMMENT '关联交易id,充值payment_trade表，消费billing_record表，退款refund_order表',
    `remark`      varchar(64)    not null default '' comment '备注',
    `channel` tinyint not null default 1 comment '充值渠道 1-后台充值';
    key idx_user_id (user_id),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户余额流水';


CREATE TABLE `user_api_key`
(
    `id`              bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`      bigint(20)  NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`     bigint(20)  NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`      bigint(20)  NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`     bigint(20)  NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`          bigint(20)  NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`      bigint(20)  NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`         bigint      not null default 0 comment '用户ID',
    `api_key`         varchar(64) not null default '' comment '秘钥',
    `remark`          varchar(64) not null default '' comment '备注',
    `last_visit_time` bigint      not null default 0 comment '最近访问时间',
    `status`          tinyint(1)  not null default 0 comment '秘钥状态 1-正常 2-吊销',
    key idx_user_id (user_id),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户秘钥管理';

# 主机套餐规格定价表（server_sku 直接从 cloudpods API 获取，接口 /api/v2/serverskus）
drop table if exists server_sku_billing_method;
CREATE TABLE `server_sku_billing_method`
(
    `id`            bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`    bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`   bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`    bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`   bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`    bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `server_sku_id` varchar(64)    not null comment '服务器套餐SKU ID',
    `billing_cycle` varchar(16)    not null default '1H' comment '计费周期 1H,1D,1M,3M,6M,1Y,2Y,3Y',
    `unit_price`    decimal(20, 4) not null default 0.0000 comment '一个计费周期的单位价格(元)',
    `site_id`       tinyint(1)     not null default 1 comment '站点ID 1-国际站',
    `status`        tinyint(1)     not null default 1 comment '状态 1-正常 2-已失效',
    `remark`        varchar(256)   not null default '' comment '备注',
    key idx_server_sku_id (server_sku_id),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='主机套餐规格定价表';


# 用户订单表
CREATE TABLE `user_order`
(
    `id`                bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`       bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`       bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`            bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`           bigint(20)     not null comment '用户ID',
    `order_no`          varchar(64)    not null comment '订单号',
    `server_sku_id`     varchar(64)    not null comment '服务器套餐SKU ID',
    `billing_method_id` bigint(20)     not null comment '计费方式ID',
    `billing_type`      tinyint(1)     not null comment '计费类型 1-按需付费 2-包年包月',
    `billing_cycle`     varchar(16)    not null comment '计费周期',
    `quantity`          int            not null default 1 comment '购买数量',
    `unit_price`        decimal(20, 4) not null comment '单价',
    `total_amount`      decimal(20, 4) not null comment '总金额',
    `discount_amount`   decimal(20, 4) not null default 0.0000 comment '优惠金额',
    `actual_amount`     decimal(20, 4) not null comment '实际支付金额',
    `currency`          varchar(8)     not null default 'CNY' comment '货币单位',
    `order_status`      tinyint(1)     not null default 1 comment '订单状态 1-待支付 2-已支付 3-已取消 4-已退款 5-已交付',
    `pay_status`        tinyint(1)     not null default 1 comment '支付状态 1-未支付 2-已支付 3-支付失败',
    `pay_time`          bigint(20)     not null default 0 comment '支付时间',
    `expire_time`       bigint(20)     not null default 0 comment '订单过期时间',
    `remark`            varchar(256)   not null default '' comment '备注',
    key idx_user_id (user_id),
    key idx_order_no (order_no),
    key idx_server_sku_id (server_sku_id),
    key idx_billing_method_id (billing_method_id),
    key idx_order_status (order_status),
    key idx_pay_status (pay_status),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户订单表';

# 用户服务器实例表
CREATE TABLE `user_server_instance`
(
    `id`              bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`      bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`     bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`      bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`     bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`          bigint(20)   NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`      bigint(20)   NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`         bigint(20)   not null comment '用户ID',
    `order_id`        bigint(20)   not null comment '订单ID',
    `server_id`       varchar(64)  not null comment '服务器实例ID(cloudpods)',
    `server_name`     varchar(128) not null comment '服务器名称',
    `server_sku_id`   varchar(64)  not null comment '服务器套餐SKU ID',
    `billing_type`    tinyint(1)   not null comment '计费类型 1-按需付费 2-包年包月',
    `billing_cycle`   varchar(16)  not null comment '计费周期',
    `instance_status` tinyint(1)   not null default 1 comment '实例状态 1-创建中 2-运行中 3-已停止 4-已销毁',
    `start_time`      bigint(20)   not null comment '开始计费时间',
    `end_time`        bigint(20)   not null default 0 comment '结束计费时间 0表示未结束',
    `expire_time`     bigint(20)   not null default 0 comment '到期时间 按需付费为0',
    `auto_renew`      tinyint(1)   not null default 0 comment '是否自动续费 0-否 1-是',
    `region_id`       varchar(64)  not null comment '区域ID',
    `zone_id`         varchar(64)  not null default '' comment '可用区ID',
    key idx_user_id (user_id),
    key idx_order_id (order_id),
    key idx_server_id (server_id),
    key idx_server_sku_id (server_sku_id),
    key idx_billing_type (billing_type),
    key idx_instance_status (instance_status),
    key idx_expire_time (expire_time),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户服务器实例表';


# 计费记录表
CREATE TABLE `billing_record`
(
    `id`                bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`       bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`       bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`            bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`           bigint(20)     not null comment '用户ID',
    `instance_id`       bigint(20)     not null comment '服务器实例ID',
    `server_id`         varchar(64)    not null comment '服务器实例ID(cloudpods)',
    `billing_type`      tinyint(1)     not null comment '计费类型 1-按需付费 2-包年包月',
    `billing_cycle`     varchar(16)    not null comment '计费周期',
    `start_time`        bigint(20)     not null comment '计费开始时间',
    `end_time`          bigint(20)     not null comment '计费结束时间',
    `duration`          bigint(20)     not null comment '计费时长(秒)',
    `unit_price`        decimal(20, 4) not null comment '单价',
    `amount`            decimal(20, 4) not null comment '计费金额',
    `currency`          varchar(8)     not null default 'CNY' comment '货币单位',
    `record_type`       tinyint(1)     not null default 1 comment '记录类型 1-正常计费 2-退费 3-补费',
    `settlement_status` tinyint(1)     not null default 1 comment '结算状态 1-未结算 2-已结算',
    `settlement_time`   bigint(20)     not null default 0 comment '结算时间',
    `remark`            varchar(256)   not null default '' comment '备注',
    key idx_user_id (user_id),
    key idx_instance_id (instance_id),
    key idx_server_id (server_id),
    key idx_billing_type (billing_type),
    key idx_start_time (start_time),
    key idx_end_time (end_time),
    key idx_settlement_status (settlement_status),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='计费记录表';


# 续费记录表
CREATE TABLE `renewal_record`
(
    `id`                   bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`           bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`          bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`           bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`          bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`               bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`           bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`              bigint(20)     not null comment '用户ID',
    `instance_id`          bigint(20)     not null comment '服务器实例ID',
    `order_id`             bigint(20)     not null comment '续费订单ID',
    `renewal_type`         tinyint(1)     not null comment '续费类型 1-手动续费 2-自动续费',
    `renewal_cycle`        varchar(16)    not null comment '续费周期',
    `renewal_amount`       decimal(20, 4) not null comment '续费金额',
    `original_expire_time` bigint(20)     not null comment '原到期时间',
    `new_expire_time`      bigint(20)     not null comment '新到期时间',
    `renewal_status`       tinyint(1)     not null default 1 comment '续费状态 1-续费中 2-续费成功 3-续费失败',
    `renewal_time`         bigint(20)     not null comment '续费时间',
    `remark`               varchar(256)   not null default '' comment '备注',
    key idx_user_id (user_id),
    key idx_instance_id (instance_id),
    key idx_order_id (order_id),
    key idx_renewal_type (renewal_type),
    key idx_renewal_status (renewal_status),
    key idx_renewal_time (renewal_time),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='续费记录表';

CREATE TABLE `payment_trade`
(
    `id`              bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`      bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`     bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`      bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`     bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`          bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`      bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`         bigint(20)     not null comment '用户ID',
    `trade_no`        varchar(64)    not null comment '平台生成的唯一交易号',
    `out_trade_no`    varchar(128)   not null default '' comment '第三方支付平台的交易流水号',
    `payment_channel` varchar(32)    not null comment '支付渠道 ALIPAY-支付宝 WECHAT_PAY-微信支付 MANUAL-手动打款',
    `trade_amount`    decimal(20, 4) not null default 0.0000 comment '交易金额',
    `trade_status`    tinyint(1)     not null default 1 comment '交易状态 1-待支付 2-支付成功 3-支付失败 4-交易关闭',
    `notify_time`     bigint(20)     not null default 0 comment '支付成功通知时间',
    `notify_content`  text comment '第三方回调的原始报文 (用于审计和排错)',
    `remark`          varchar(256)   not null default '' comment '备注',
    unique key uk_trade_no (trade_no),
    key idx_user_id (user_id),
    key idx_out_trade_no (out_trade_no),
    key idx_trade_status (trade_status),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付交易表';


CREATE TABLE `refund_order`
(
    `id`                bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid`       bigint(20)     NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid`       bigint(20)     NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`            bigint(20)     NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`        bigint(20)     NOT NULL DEFAULT '0' COMMENT '删除时间',
    `user_id`           bigint(20)     not null comment '用户ID',
    `original_order_id` bigint(20)     not null comment '关联的原始订单ID (user_order.id)',
    `instance_id`       bigint(20)     not null comment '退款的服务器实例ID (user_server_instance.id)',
    `refund_no`         varchar(64)    not null comment '平台生成的唯一退款单号',
    `out_refund_no`     varchar(128)   not null default '' comment '第三方支付平台的退款流水号',
    `reason`            varchar(256)   not null default '' comment '退款原因',
    `total_amount`      decimal(20, 4) not null comment '原订单金额',
    `refund_amount`     decimal(20, 4) not null comment '实际退款金额 (可能涉及按比例退款)',
    `refund_channel`    varchar(32)    not null comment '退款渠道 ORIGINAL-原路退回 BALANCE-退至余额',
    `refund_status`     tinyint(1)     not null default 1 comment '退款状态 1-待审核 2-审核通过(退款中) 3-退款成功 4-退款失败 5-审核驳回',
    `success_time`      bigint(20)     not null default 0 comment '退款成功时间',
    `remark`            varchar(256)   not null default '' comment '备注(如驳回原因)',
    unique key uk_refund_no (refund_no),
    key idx_user_id (user_id),
    key idx_original_order_id (original_order_id),
    key idx_instance_id (instance_id),
    key idx_refund_status (refund_status),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='退款订单表';


-- 管理后台的用户表
CREATE TABLE `admin_user`
(
    `id`          bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `created_at`  bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建时间',
    `created_uid` bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建人',
    `updated_at`  bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新时间',
    `updated_uid` bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新人',
    `is_del`      bigint(20)   NOT NULL DEFAULT '0' COMMENT '是否已删除',
    `deleted_at`  bigint(20)   NOT NULL DEFAULT '0' COMMENT '删除时间',
    `username`    varchar(128) not null default '' comment '用户名',
    `password`    varchar(256) not null default '' comment '密码',
    `avatar`      varchar(256) not null default '' comment '头像',
    `nickname`    varchar(32)  not null default '' comment '昵称',
    key idx_username (username),
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='后台用户表';






