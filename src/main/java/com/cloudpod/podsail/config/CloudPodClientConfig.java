package com.cloudpod.podsail.config;

import com.yunionyun.mcp.mcclient.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "cloudpods.client")
public class CloudPodClientConfig {

    private String authUrl;
    private String user;
    private String password;
    private String project;
    private String userDomain;
    private String projectDomain;
    private String defaultRegion = "region0";
    private String defaultZone = "zone0";

    @Bean
    public Client mcClient() {
        return new Client(authUrl, 30, true, false);
    }

    @Bean
    public AuthAgent authAgent() {
        AuthAgent authAgent = new AuthAgent(authUrl, userDomain, user, password, project, 1024, 60, true, true);
        authAgent.start_sync_ready();
        authAgent.start();
        return authAgent;
    }
}
