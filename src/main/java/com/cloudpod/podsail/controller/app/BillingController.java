package com.cloudpod.podsail.controller.app;

import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.service.billing.BillingEngineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 计费管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@RestController
@RequestMapping("/api/billing")
@Api(tags = "计费管理", description = "计费引擎相关操作接口")
public class BillingController {

    @Autowired
    private BillingEngineService billingEngineService;

    @PostMapping("/start/{instanceId}")
    @ApiOperation(value = "启动实例计费", notes = "手动启动指定实例的计费")
    public Response<Boolean> startBilling(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("instanceId") Long instanceId) {
        
        log.info("手动启动实例计费: instanceId={}", instanceId);
        boolean result = billingEngineService.startBilling(instanceId);
        return Response.success(result);
    }

    @PostMapping("/stop/{instanceId}")
    @ApiOperation(value = "停止实例计费", notes = "手动停止指定实例的计费")
    public Response<Boolean> stopBilling(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("instanceId") Long instanceId) {
        
        log.info("手动停止实例计费: instanceId={}", instanceId);
        boolean result = billingEngineService.stopBilling(instanceId);
        return Response.success(result);
    }

    @PostMapping("/execute-periodic")
    @ApiOperation(value = "执行周期性计费", notes = "手动触发周期性计费任务")
    public Response<Integer> executePeriodicBilling() {
        
        log.info("手动执行周期性计费");
        int processedCount = billingEngineService.executePeriodicBilling();
        return Response.success(processedCount);
    }

    @PostMapping("/check-balance")
    @ApiOperation(value = "检查余额不足", notes = "检查并处理余额不足的实例")
    public Response<Integer> checkInsufficientBalance() {
        
        log.info("手动检查余额不足");
        int destroyedCount = billingEngineService.checkAndHandleInsufficientBalance();
        return Response.success(destroyedCount);
    }

    @PostMapping("/check-expired")
    @ApiOperation(value = "检查到期实例", notes = "检查并处理到期的包年包月实例")
    public Response<Integer> checkExpiredInstances() {
        
        log.info("手动检查到期实例");
        int destroyedCount = billingEngineService.checkAndHandleExpiredInstances();
        return Response.success(destroyedCount);
    }
}
