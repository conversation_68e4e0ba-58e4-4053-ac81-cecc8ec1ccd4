package com.cloudpod.podsail.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.controller.base.BaseController;
import com.cloudpod.podsail.dto.billing.BillingRecordCreateDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordQueryDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordResponseDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordUpdateDTO;
import com.cloudpod.podsail.service.BillingRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 计费记录管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/billing-records")
@Api(tags = "计费记录管理", description = "计费记录相关的CRUD操作接口")
public class BillingRecordController extends BaseController {

    @Autowired
    private BillingRecordService billingRecordService;

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询计费记录", notes = "根据计费记录ID查询详细信息")
    public Response<BillingRecordResponseDTO> getBillingRecordById(
            @ApiParam(value = "计费记录ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询计费记录，ID: {}", id);
        BillingRecordResponseDTO responseDTO = billingRecordService.getBillingRecordById(id);
        return Response.success(responseDTO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询计费记录", notes = "根据条件分页查询计费记录列表")
    public Response<IPage<BillingRecordResponseDTO>> getBillingRecordPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid BillingRecordQueryDTO queryDTO) {
        
        log.info("分页查询计费记录，查询条件: {}", queryDTO);
        IPage<BillingRecordResponseDTO> page = billingRecordService.getBillingRecordPage(queryDTO);
        return Response.success(page);
    }
}
