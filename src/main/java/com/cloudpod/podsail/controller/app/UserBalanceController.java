package com.cloudpod.podsail.controller.app;

import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.domain.app.query.AppUserBalanceLogQuery;
import com.cloudpod.podsail.domain.app.vo.AppUserBalanceListVO;
import com.cloudpod.podsail.service.app.UserBalanceService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @className UserBalanceController
 * @date 2025/8/21 22:43
 */
@RestController
@RequestMapping("/api/user/balance")
@Api(value = "用户余额管理", tags = "用户余额管理")
public class UserBalanceController extends BaseController {

    @Autowired
    private UserBalanceService userBalanceService;

    @GetMapping("/list")
    public Response<List<AppUserBalanceListVO>> getBalanceList(AppUserBalanceLogQuery appUserBalanceLogQuery) {
        return Response.success(userBalanceService.getBalanceLog(appUserBalanceLogQuery));
    }
}
