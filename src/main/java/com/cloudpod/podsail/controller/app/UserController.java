package com.cloudpod.podsail.controller.app;

import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.common.util.EmailSendManage;
import com.cloudpod.podsail.domain.app.form.*;
import com.cloudpod.podsail.domain.app.vo.AppUserInfoVO;
import com.cloudpod.podsail.service.app.UserService;
import io.swagger.annotations.Api;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @className UserContrpller
 * @date 2025/8/20 20:41
 */
@RestController("appUserController")
@RequestMapping("/api/user")
@Api(value = "用户管理", tags = "用户管理")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @PostMapping("/sendEmail")
    public Response<Boolean> sendEmail(@Valid @RequestBody AppEmailForm appEmailForm) {
        return Response.success(userService.sendEmail(appEmailForm));
    }

    @PostMapping("/register")
    public Response<AppUserInfoVO> register(@Valid @RequestBody AppUserRegisterForm appUserRegisterForm) {
        return Response.success(userService.register(appUserRegisterForm));
    }

    @PostMapping("/passwordLogin")
    public Response<AppUserInfoVO> passwordLogin(@Valid @RequestBody AppUserPasswordLoginForm appUserPasswordLoginForm) {
        return Response.success(userService.passwordLogin(appUserPasswordLoginForm));
    }
    @PostMapping("/verifyLogin")
    public Response<AppUserInfoVO> verifyLogin(@Valid @RequestBody AppUserVerifyLoginForm appUserVerifyLoginForm) {
        return Response.success(userService.verifyLogin(appUserVerifyLoginForm));
    }

    @PostMapping("/updatePassword")
    public Response<Boolean> updatePassword(@Valid @RequestBody AppUserUpdatePasswordForm appUserUpdatePasswordForm) {
        return Response.success(userService.updatePassword(appUserUpdatePasswordForm));
    }


    @PostMapping("/forgetPassword")
    public Response<Boolean> forgetPassword(@Valid @RequestBody AppUserForgetPasswordForm appUserForgetPasswordForm) {
        return Response.success(userService.forgetPassword(appUserForgetPasswordForm));
    }
}
