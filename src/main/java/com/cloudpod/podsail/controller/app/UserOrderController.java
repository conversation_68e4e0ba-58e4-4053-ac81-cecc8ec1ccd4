package com.cloudpod.podsail.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.order.UserOrderCreateDTO;
import com.cloudpod.podsail.dto.order.UserOrderQueryDTO;
import com.cloudpod.podsail.dto.order.UserOrderResponseDTO;
import com.cloudpod.podsail.dto.order.UserOrderUpdateDTO;
import com.cloudpod.podsail.service.UserOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户订单管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user-orders")
@Api(tags = "用户订单管理", description = "用户订单相关的CRUD操作接口")
public class UserOrderController {
    
    @Autowired
    private UserOrderService userOrderService;
    
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户订单", notes = "根据用户订单ID查询详细信息")
    public Response<UserOrderResponseDTO> getUserOrderById(
            @ApiParam(value = "用户订单ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询用户订单，ID: {}", id);
        UserOrderResponseDTO responseDTO = userOrderService.getUserOrderById(id);
        return Response.success(responseDTO);
    }
    
    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户订单", notes = "根据条件分页查询用户订单列表")
    public Response<IPage<UserOrderResponseDTO>> getUserOrderPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserOrderQueryDTO queryDTO) {
        
        log.info("分页查询用户订单，查询条件: {}", queryDTO);
        IPage<UserOrderResponseDTO> page = userOrderService.getUserOrderPage(queryDTO);
        return Response.success(page);
    }
    
}
