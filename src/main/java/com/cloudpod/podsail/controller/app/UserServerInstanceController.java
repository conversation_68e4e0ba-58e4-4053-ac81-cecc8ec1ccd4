package com.cloudpod.podsail.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceQueryDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;

import com.cloudpod.podsail.service.UserServerInstanceService;
import com.cloudpod.podsail.service.instance.InstanceLifecycleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 用户服务器实例管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user-server-instances")
@Api(tags = "用户服务器实例管理", description = "用户服务器实例相关的CRUD操作接口")
public class UserServerInstanceController {

    @Autowired
    private UserServerInstanceService userServerInstanceService;

    @Autowired
    private InstanceLifecycleService instanceLifecycleService;

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户服务器实例", notes = "根据用户服务器实例ID查询详细信息")
    public Response<UserServerInstanceResponseDTO> getUserServerInstanceById(
            @ApiParam(value = "用户服务器实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询用户服务器实例，ID: {}", id);
        UserServerInstanceResponseDTO responseDTO = userServerInstanceService.getUserServerInstanceById(id);
        return Response.success(responseDTO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户服务器实例", notes = "根据条件分页查询用户服务器实例列表")
    public Response<IPage<UserServerInstanceResponseDTO>> getUserServerInstancePage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserServerInstanceQueryDTO queryDTO) {
        
        log.info("分页查询用户服务器实例，查询条件: {}", queryDTO);
        IPage<UserServerInstanceResponseDTO> page = userServerInstanceService.getUserServerInstancePage(queryDTO);
        return Response.success(page);
    }

    // ==================== 实例生命周期管理API ====================

    @PostMapping("/create")
    @ApiOperation(value = "创建服务器实例", notes = "创建虚拟机或裸机实例，系统会根据参数自动判断类型")
    public Response<UserServerInstanceResponseDTO> createVirtualMachine(
            @ApiParam(value = "创建参数", required = true)
            @RequestBody @Valid UserServerInstanceCreateDTO createDTO) {
        
        log.info("创建服务器实例，参数: {}", createDTO);
        UserServerInstanceResponseDTO responseDTO = instanceLifecycleService.createVirtualMachine(createDTO);
        return Response.success(responseDTO);
    }

    @PostMapping("/create-baremetal")
    @ApiOperation(value = "创建裸机实例", notes = "专门用于创建裸机实例的接口")
    public Response<UserServerInstanceResponseDTO> createBaremetalInstance(
            @ApiParam(value = "创建参数", required = true)
            @RequestBody @Valid UserServerInstanceCreateDTO createDTO) {
        
        // 强制设置为裸机类型
        createDTO.setInstanceType("baremetal");
        log.info("创建裸机实例，参数: {}", createDTO);
        UserServerInstanceResponseDTO responseDTO = instanceLifecycleService.createVirtualMachine(createDTO);
        return Response.success(responseDTO);
    }

    @PostMapping("/create-vm")
    @ApiOperation(value = "创建虚拟机实例", notes = "专门用于创建虚拟机实例的接口")
    public Response<UserServerInstanceResponseDTO> createVmInstance(
            @ApiParam(value = "创建参数", required = true)
            @RequestBody @Valid UserServerInstanceCreateDTO createDTO) {
        
        // 强制设置为虚拟机类型
        createDTO.setInstanceType("kvm");
        log.info("创建虚拟机实例，参数: {}", createDTO);
        UserServerInstanceResponseDTO responseDTO = instanceLifecycleService.createVirtualMachine(createDTO);
        return Response.success(responseDTO);
    }

    @PostMapping("/{id}/start")
    @ApiOperation(value = "启动虚拟机实例", notes = "启动指定的虚拟机实例")
    public Response<Boolean> startVirtualMachine(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("启动虚拟机实例: {}", id);
        boolean result = instanceLifecycleService.startVirtualMachine(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/stop")
    @ApiOperation(value = "停止虚拟机实例", notes = "停止指定的虚拟机实例")
    public Response<Boolean> stopVirtualMachine(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "是否强制停止", required = false)
            @RequestParam(value = "force", defaultValue = "false") Boolean force) {
        
        log.info("停止虚拟机实例: {}, 强制: {}", id, force);
        boolean result = instanceLifecycleService.stopVirtualMachine(id, force);
        return Response.success(result);
    }

    @PostMapping("/{id}/reboot")
    @ApiOperation(value = "重启虚拟机实例", notes = "重启指定的虚拟机实例")
    public Response<Boolean> rebootVirtualMachine(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "是否强制重启", required = false)
            @RequestParam(value = "force", defaultValue = "false") Boolean force) {
        
        log.info("重启虚拟机实例: {}, 强制: {}", id, force);
        boolean result = instanceLifecycleService.rebootVirtualMachine(id, force);
        return Response.success(result);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "销毁虚拟机实例", notes = "销毁指定的虚拟机实例")
    public Response<Boolean> destroyVirtualMachine(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @ApiParam(value = "是否强制销毁", required = false)
            @RequestParam(value = "force", defaultValue = "false") Boolean force) {
        
        log.info("销毁虚拟机实例: {}, 强制: {}", id, force);
        boolean result = instanceLifecycleService.destroyVirtualMachine(id, force);
        return Response.success(result);
    }

    // 调整虚拟机配置接口已删除（本期不做）

    @PostMapping("/list-baremetal")
    @ApiOperation(value = "查询裸机实例列表", notes = "分页查询裸机实例列表")
    public Response<IPage<UserServerInstanceResponseDTO>> getBaremetalInstancePage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserServerInstanceQueryDTO queryDTO) {
        
        log.info("分页查询裸机实例，查询条件: {}", queryDTO);
        IPage<UserServerInstanceResponseDTO> page = userServerInstanceService.getUserServerInstancePage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/list-vm")
    @ApiOperation(value = "查询虚拟机实例列表", notes = "分页查询虚拟机实例列表")
    public Response<IPage<UserServerInstanceResponseDTO>> getVmInstancePage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid UserServerInstanceQueryDTO queryDTO) {
        
        log.info("分页查询虚拟机实例，查询条件: {}", queryDTO);
        IPage<UserServerInstanceResponseDTO> page = userServerInstanceService.getUserServerInstancePage(queryDTO);
        return Response.success(page);
    }

    @PostMapping("/{id}/sync")
    @ApiOperation(value = "同步虚拟机状态", notes = "从CloudPods同步虚拟机最新状态")
    public Response<Boolean> syncVirtualMachineStatus(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("同步虚拟机状态: {}", id);
        boolean result = instanceLifecycleService.syncVirtualMachineStatus(id);
        return Response.success(result);
    }

    @GetMapping("/{id}/detail")
    @ApiOperation(value = "获取虚拟机最新详情", notes = "获取虚拟机最新状态详情（会先同步状态）")
    public Response<UserServerInstanceResponseDTO> getVirtualMachineDetail(
            @ApiParam(value = "实例ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("获取虚拟机最新详情: {}", id);
        UserServerInstanceResponseDTO responseDTO = instanceLifecycleService.getVirtualMachineDetail(id);
        return Response.success(responseDTO);
    }

    @PostMapping("/sync-all")
    @ApiOperation(value = "批量同步运行中实例状态", notes = "批量同步所有运行中实例的状态（管理员操作）")
    public Response<Integer> syncAllRunningVirtualMachineStatus() {
        
        log.info("开始批量同步运行中实例状态");
        int syncCount = instanceLifecycleService.syncAllRunningVirtualMachineStatus();
        return Response.success(syncCount);
    }
}
