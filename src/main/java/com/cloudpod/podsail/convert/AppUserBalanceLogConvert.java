package com.cloudpod.podsail.convert;

import com.cloudpod.podsail.db.entity.User;
import com.cloudpod.podsail.db.entity.UserBalanceLog;
import com.cloudpod.podsail.domain.app.vo.AppUserBalanceListVO;
import com.cloudpod.podsail.domain.app.vo.AppUserInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @className AppUserConvert
 * @date 2025/8/20 21:47
 */
@Mapper
public interface AppUserBalanceLogConvert {
    AppUserBalanceLogConvert INSTANCE = Mappers.getMapper(AppUserBalanceLogConvert.class);

    @Mapping(target = "type", expression = "java(userBalanceLog.getType().getValue())")
    AppUserBalanceListVO toListVO(UserBalanceLog userBalanceLog);

    List<AppUserBalanceListVO> toListVOs(List<UserBalanceLog> userBalanceLogs);
}
