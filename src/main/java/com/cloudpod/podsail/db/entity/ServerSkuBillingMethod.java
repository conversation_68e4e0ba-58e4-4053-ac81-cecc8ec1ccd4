package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 主机套餐规格定价表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("server_sku_billing_method")
public class ServerSkuBillingMethod extends Entity<ServerSkuBillingMethod> {

    private static final long serialVersionUID = 1L;

    /**
     * 服务器套餐SKU ID
     */
    @TableField("server_sku_id")
    private String serverSkuId;

    /**
     * 计费周期 按需付费:1H,1D 包年包月:1M,3M,6M,1Y,2Y,3Y
     */
    @TableField("billing_cycle")
    private String billingCycle;

    /**
     * 价格(元)
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 状态 1-正常 2-已失效
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 站点ID 1-国际站
     */
    @TableField("site_id")
    private Integer siteId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
