package com.cloudpod.podsail.db.enums;

import com.cloudpod.podsail.common.base.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @className BooleanEnum
 * @date 2024/9/10 14:43
 * @description: TODO
 */
@AllArgsConstructor
@Getter
public enum BooleanEnum implements IEnum<Integer> {
    TRUE(1,"是"),
    FALSE(0,"否"),
    ;
    private final Integer value;
    private final String desc;

    public Boolean toBoolean() {
        return TRUE.equals(this);
    }
}
