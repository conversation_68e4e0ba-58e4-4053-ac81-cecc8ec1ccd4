package com.cloudpod.podsail.db.enums;

import com.cloudpod.podsail.common.base.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @className BooleanEnum
 * @date 2024/9/10 14:43
 * @description: TODO
 */
@AllArgsConstructor
@Getter
public enum UserBalanceLogTypeEnum implements IEnum<Integer> {
    CHARGE(1,"充值"),
    RESUME(2,"消费"),
    REFUND(3,"退款"),
    ;
    private final Integer value;
    private final String desc;
}
