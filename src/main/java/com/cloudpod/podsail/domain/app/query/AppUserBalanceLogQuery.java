package com.cloudpod.podsail.domain.app.query;

import com.cloudpod.podsail.common.base.dto.TimestampRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * @className AppUserBalanceLogQuery
 * <AUTHOR>
 * @date 2025/8/22 22:17
 * @description: TODO 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "用户充值记录", description = "充值记录")
public class AppUserBalanceLogQuery extends TimestampRange {

    @ApiModelProperty(hidden = true)
    private Long userId;
}
