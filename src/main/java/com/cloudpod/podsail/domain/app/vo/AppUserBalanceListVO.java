package com.cloudpod.podsail.domain.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @className AppUserBalanceListVO
 * <AUTHOR>
 * @date 2025/8/22 21:53
 * @description: TODO 
 */
@Data
@ApiModel(value = "用户充值记录")
public class AppUserBalanceListVO {

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("类型 1-充值 2-消费 3-退款")
    private Integer type;

    @ApiModelProperty("重置渠道 1-后台充值")
    private Integer channel;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Long createAt;

}
