package com.cloudpod.podsail.service.app.impl;

import com.cloudpod.podsail.convert.AppUserBalanceLogConvert;
import com.cloudpod.podsail.db.dao.UserBalanceLogDao;
import com.cloudpod.podsail.db.entity.UserBalanceLog;
import com.cloudpod.podsail.db.enums.UserBalanceLogTypeEnum;
import com.cloudpod.podsail.domain.app.query.AppUserBalanceLogQuery;
import com.cloudpod.podsail.domain.app.vo.AppUserBalanceListVO;
import com.cloudpod.podsail.service.app.UserBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * @className UserBalanceServiceImpl
 * <AUTHOR>
 * @date 2025/8/22 21:28
 * @description: TODO 
 */
@Service
public class UserBalanceServiceImpl implements UserBalanceService {

    @Autowired
    private UserBalanceLogDao userBalanceLogDao;

    @Override
    public List<AppUserBalanceListVO> getBalanceLog(AppUserBalanceLogQuery appUserBalanceLogQuery) {
        List<UserBalanceLog> balanceLogs = userBalanceLogDao.lambdaQuery()
                .eq(UserBalanceLog::getType, UserBalanceLogTypeEnum.CHARGE)
                .eq(UserBalanceLog::getUserId, appUserBalanceLogQuery.getUserId())
                .ge(UserBalanceLog::getCreatedAt, appUserBalanceLogQuery.getTimeStart())
                .le(UserBalanceLog::getCreatedAt, appUserBalanceLogQuery.getTimeEnd())
                .list();
        return AppUserBalanceLogConvert.INSTANCE.toListVOs(balanceLogs);
    }
}
