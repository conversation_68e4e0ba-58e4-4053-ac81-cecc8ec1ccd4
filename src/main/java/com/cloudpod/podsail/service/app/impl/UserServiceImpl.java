package com.cloudpod.podsail.service.app.impl;
import java.math.BigDecimal;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.jwt.JWTUtil;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.EmailSendManage;
import com.cloudpod.podsail.config.PasswordConfig;
import com.cloudpod.podsail.convert.AppUserConvert;
import com.cloudpod.podsail.db.dao.UserDao;
import com.cloudpod.podsail.db.entity.User;
import com.cloudpod.podsail.domain.app.consts.AppRedisConstants;
import com.cloudpod.podsail.domain.app.form.*;
import com.cloudpod.podsail.domain.app.vo.AppUserInfoVO;
import com.cloudpod.podsail.service.app.UserService;
import com.cloudpod.podsail.service.auth.TokenService;
import com.cloudpod.podsail.utils.SiteUtil;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;

/**
 * <AUTHOR>
 * @className UserServiceImpl
 * @date 2025/8/19 20:41
 */
@Service("appUserService")
public class UserServiceImpl implements UserService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private UserDao userDao;
    @Autowired
    private EmailSendManage emailSendManage;
    @Autowired
    private PasswordConfig passwordConfig;
    @Autowired
    private TokenService tokenService;

    @Override
    public Boolean sendEmail(AppEmailForm appEmailForm) {
        // todo 后续加RateLimit
        String verifyCode = RandomUtil.randomNumbers(6);
        RBucket<String> redissonClientBucket = redissonClient.getBucket(String.format(AppRedisConstants.APP_USER_SEND_EMAIL_REDIS_KEY, appEmailForm.getUsername()),
                new TypedJsonJacksonCodec(String.class));
        redissonClientBucket.set(verifyCode, Duration.ofMinutes(5));
        return Boolean.TRUE;
    }

    @Override
    public AppUserInfoVO register(AppUserRegisterForm appUserRegisterForm) {
        RBucket<String> redissonClientBucket = redissonClient.getBucket(String.format(AppRedisConstants.APP_USER_SEND_EMAIL_REDIS_KEY, appUserRegisterForm.getUsername()),
                new TypedJsonJacksonCodec(String.class));
        String code = redissonClientBucket.get();
        if (!Objects.equals(code, appUserRegisterForm.getVerifyCode())) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "验证码已失效，请重新获取");
        }
        Optional<User> userOpt = userDao.lambdaQuery().eq(User::getUsername, appUserRegisterForm.getUsername()).oneOpt();
        if (userOpt.isPresent()) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "用户已存在");
        }
        User user = new User();
        user.setUsername(appUserRegisterForm.getUsername());
        user.setPassword(BCrypt.hashpw(appUserRegisterForm.getPassword(), passwordConfig.getUserSalt()));
        user.setCode(RandomUtil.randomStringUpper(10));
        user.setBalance(new BigDecimal("0"));
        user.setRegisterTime(DateUtil.currentSeconds());
        user.setSite(SiteUtil.getSite());
        userDao.save(user);

        return buildInfo(user);
    }

    @Override
    public AppUserInfoVO passwordLogin(AppUserPasswordLoginForm appUserPasswordLoginForm) {
        Optional<User> userOpt = userDao.lambdaQuery().eq(User::getUsername, appUserPasswordLoginForm.getUsername()).oneOpt();
        if (userOpt.isEmpty()) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "用户不存在");
        }
        User user = userOpt.get();
        if (!BCrypt.checkpw(appUserPasswordLoginForm.getPassword(), user.getPassword())) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "密码错误");
        }
        return buildInfo(user);
    }

    @Override
    public AppUserInfoVO verifyLogin(AppUserVerifyLoginForm appUserVerifyLoginForm) {
        RBucket<String> redissonClientBucket = redissonClient.getBucket(String.format(AppRedisConstants.APP_USER_SEND_EMAIL_REDIS_KEY, appUserVerifyLoginForm.getUsername()),
                new TypedJsonJacksonCodec(String.class));
        String code = redissonClientBucket.get();
        if (!Objects.equals(code, appUserVerifyLoginForm.getVerifyCode())) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "验证码已失效，请重新获取");
        }
        Optional<User> userOpt = userDao.lambdaQuery().eq(User::getUsername, appUserVerifyLoginForm.getUsername()).oneOpt();
        if (userOpt.isEmpty()) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "用户不存在");
        }
        User user = userOpt.get();
        return buildInfo(user);
    }

    @Override
    public Boolean updatePassword(AppUserUpdatePasswordForm appUserUpdatePasswordForm) {
        Optional<User> userOpt = userDao.lambdaQuery().eq(User::getUsername, appUserUpdatePasswordForm.getUsername()).oneOpt();
        if (userOpt.isEmpty()) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "用户不存在");
        }
        User user = userOpt.get();
        if (!BCrypt.checkpw(appUserUpdatePasswordForm.getPassword(), user.getPassword())) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "旧密码错误");
        }
        user.setPassword(BCrypt.hashpw(appUserUpdatePasswordForm.getNewPassword(), passwordConfig.getUserSalt()));
        return user.updateById();
    }

    @Override
    public Boolean forgetPassword(AppUserForgetPasswordForm appUserForgetPasswordForm) {
        RBucket<String> redissonClientBucket = redissonClient.getBucket(String.format(AppRedisConstants.APP_USER_SEND_EMAIL_REDIS_KEY, appUserForgetPasswordForm.getUsername()),
                new TypedJsonJacksonCodec(String.class));
        String code = redissonClientBucket.get();
        if (!Objects.equals(code, appUserForgetPasswordForm.getVerifyCode())) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "验证码已失效，请重新获取");
        }
        Optional<User> userOpt = userDao.lambdaQuery().eq(User::getUsername, appUserForgetPasswordForm.getUsername()).oneOpt();
        if (userOpt.isEmpty()) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "用户不存在");
        }
        User user = userOpt.get();
        user.setPassword(BCrypt.hashpw(appUserForgetPasswordForm.getNewPassword(), passwordConfig.getUserSalt()));
        return user.updateById();
    }

    AppUserInfoVO buildInfo(User user) {
        AppUserInfoVO appUserInfoVO = AppUserConvert.INSTANCE.appUserToInfoVO(user);
        appUserInfoVO.setToken(tokenService.createToken(user.getId(), passwordConfig.getUserSalt()));
        return appUserInfoVO;
    }
}
