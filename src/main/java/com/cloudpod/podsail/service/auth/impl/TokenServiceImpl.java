package com.cloudpod.podsail.service.auth.impl;

import cn.hutool.core.convert.NumberWithFormat;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.config.PasswordConfig;
import com.cloudpod.podsail.service.auth.TokenService;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @className TokenServiceImpl
 * @date 2025/8/19 21:03
 */
@Service
public class TokenServiceImpl implements TokenService {

    @Autowired
    private PasswordConfig passwordConfig;

    @Override
    public String createToken(Long userId, String salt) {
        long expireAt = passwordConfig.getExpireTime() + DateUtil.currentSeconds();
        HashMap<String, Object> payload = MapUtil.of("userId", userId);
        payload.put("expireAt", expireAt);
        return JWTUtil.createToken(payload, salt.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public Long verifyToken(String token, String salt) {
        token = StrUtil.strip(token, "Bearer ");
        if (!JWTUtil.verify(token, salt.getBytes(StandardCharsets.UTF_8))) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.TOKEN_VALID_FAILED);
        }
        JWT jwt = JWTUtil.parseToken(token);
        Long expireAt = getLong(jwt, "expireAt");
        if (expireAt < DateUtil.currentSeconds()) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.TOKEN_TIMEOUT);
        }
        Long userId = getLong(jwt, "userId");
        if (userId == 0L) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.TOKEN_VALID_FAILED);
        }
        return (Long) userId;
    }

    private Long getLong(JWT jet, String field) {
        Object payload = jet.getPayload(field);
        if (payload == null) {
            return 0L;
        }
        Integer number = (Integer)((NumberWithFormat) payload).getNumber();
        return number.longValue();
    }
}
