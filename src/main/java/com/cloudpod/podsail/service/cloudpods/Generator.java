package com.cloudpod.podsail.service.cloudpods;

import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * API文档解析器，用于解析Apifox API文档并生成DTO类
 */
public class Generator {
    
    private static final String BASE_URL = "https://s.apifox.cn/f917f6a6-db9f-4d6a-bbc3-ea58c945d7fd/";
    private static final String OUTPUT_DIR = "src/main/java/com/cloudpod/podsail/service/cloudpods/domain/";
    private static final Scanner scanner = new Scanner(System.in);
    
    public static void main(String[] args) {
        System.out.println("=== CloudPods API DTO Generator ===");

        // 如果有命令行参数，直接使用测试模式
        if (args.length > 0 && "test".equals(args[0])) {
            System.out.println("运行测试模式...");
            try {
                testSingleApi();
                return;
            } catch (Exception e) {
                System.err.println("测试失败: " + e.getMessage());
                e.printStackTrace();
                return;
            }
        }

        System.out.println("1. 解析所有接口");
        System.out.println("2. 搜索并选择接口");
        System.out.print("请选择操作 (1/2): ");

        String choice = scanner.nextLine().trim();

        try {
            switch (choice) {
                case "1":
                    parseAllApis();
                    break;
                case "2":
                    searchAndSelectApis();
                    break;
                default:
                    System.out.println("无效选择");
                    return;
            }
        } catch (Exception e) {
            System.err.println("解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试单个API解析
     */
    private static void testSingleApi() throws Exception {
        System.out.println("正在测试单个API解析...");

        ApiInfo apiInfo = new ApiInfo();
        apiInfo.url = BASE_URL + "api-86314553.md";
        apiInfo.name = "用户信用凭证列表";
        apiInfo.service = "keystone(认证服务)";
        apiInfo.resource = "credential";

        processApi(apiInfo);
        System.out.println("测试完成！");
    }
    
    /**
     * 解析所有API接口
     */
    private static void parseAllApis() throws Exception {
        System.out.println("正在获取API列表...");
        String apiListContent = fetchContent(BASE_URL + "llms.txt");
        
        // 解析API列表
        List<ApiInfo> apiList = parseApiList(apiListContent);
        System.out.println("找到 " + apiList.size() + " 个API接口");
        
        // 按服务分组
        Map<String, List<ApiInfo>> serviceGroups = groupByService(apiList);
        
        for (Map.Entry<String, List<ApiInfo>> entry : serviceGroups.entrySet()) {
            String service = entry.getKey();
            List<ApiInfo> apis = entry.getValue();
            
            System.out.println("正在处理服务: " + service + " (" + apis.size() + " 个接口)");
            
            for (ApiInfo api : apis) {
                try {
                    processApi(api);
                } catch (Exception e) {
                    System.err.println("处理接口失败: " + api.name + " - " + e.getMessage());
                }
            }
        }
        
        System.out.println("所有接口处理完成！");
    }
    
    /**
     * 搜索并选择API接口
     */
    private static void searchAndSelectApis() throws Exception {
        System.out.println("正在获取API列表...");
        String apiListContent = fetchContent(BASE_URL + "llms.txt");

        // 解析API列表
        List<ApiInfo> apiList = parseApiList(apiListContent);
        System.out.println("找到 " + apiList.size() + " 个API接口");

        while (true) {
            System.out.println("\n=== API接口搜索 ===");
            System.out.print("请输入搜索关键词 (输入 'quit' 退出): ");
            String keyword = scanner.nextLine().trim();

            if ("quit".equalsIgnoreCase(keyword)) {
                break;
            }

            if (keyword.isEmpty()) {
                System.out.println("请输入搜索关键词");
                continue;
            }

            // 搜索匹配的API
            List<ApiInfo> matchedApis = searchApis(apiList, keyword);

            if (matchedApis.isEmpty()) {
                System.out.println("未找到匹配的API接口");
                continue;
            }

            // 显示搜索结果并让用户选择
            selectAndProcessApis(matchedApis);
        }
    }
    
    /**
     * 处理单个API
     */
    private static void processApi(ApiInfo apiInfo) throws Exception {
        System.out.println("正在处理: " + apiInfo.name);
        
        // 获取API文档内容
        String content = fetchContent(apiInfo.url);
        
        // 提取OpenAPI规范
        String yamlContent = extractOpenApiSpec(content);
        if (yamlContent == null) {
            System.out.println("未找到OpenAPI规范: " + apiInfo.name);
            return;
        }
        
        // 解析YAML
        Yaml yaml = new Yaml();
        Map<String, Object> openApiSpec = yaml.load(yamlContent);
        
        // 生成DTO类
        generateDtoClasses(openApiSpec, apiInfo);
    }
    
    /**
     * 生成DTO类
     */
    private static void generateDtoClasses(Map<String, Object> openApiSpec, ApiInfo apiInfo) throws Exception {
        Map<String, Object> components = (Map<String, Object>) openApiSpec.get("components");
        if (components == null) return;
        
        Map<String, Object> schemas = (Map<String, Object>) components.get("schemas");
        if (schemas == null) return;
        
        // 确定包名
        String packageName = determinePackageName(apiInfo);
        
        for (Map.Entry<String, Object> entry : schemas.entrySet()) {
            String schemaName = entry.getKey();
            Map<String, Object> schema = (Map<String, Object>) entry.getValue();
            
            generateDtoClass(schemaName, schema, packageName);
        }
    }
    
    /**
     * 生成单个DTO类
     */
    private static void generateDtoClass(String className, Map<String, Object> schema, String packageName) throws Exception {
        StringBuilder sb = new StringBuilder();
        
        // 包声明
        sb.append("package ").append(packageName).append(";\n\n");
        
        // 导入
        sb.append("import com.alibaba.fastjson2.annotation.JSONField;\n");
        sb.append("import lombok.Data;\n");
        sb.append("import java.time.LocalDateTime;\n");
        sb.append("import java.util.List;\n");
        sb.append("import java.util.Map;\n\n");
        
        // 类声明
        sb.append("/**\n");
        sb.append(" * ").append(className).append(" DTO\n");
        sb.append(" * 自动生成，请勿手动修改\n");
        sb.append(" */\n");
        sb.append("@Data\n");
        sb.append("public class ").append(className).append(" {\n\n");
        
        // 属性
        Map<String, Object> properties = (Map<String, Object>) schema.get("properties");
        if (properties != null) {
            for (Map.Entry<String, Object> prop : properties.entrySet()) {
                String fieldName = prop.getKey();
                Map<String, Object> fieldSchema = (Map<String, Object>) prop.getValue();
                
                generateField(sb, fieldName, fieldSchema);
            }
        }
        
        sb.append("}\n");
        
        // 写入文件
        writeToFile(className, sb.toString(), packageName);
    }
    
    /**
     * 生成字段
     */
    private static void generateField(StringBuilder sb, String fieldName, Map<String, Object> fieldSchema) {
        String description = (String) fieldSchema.get("description");
        String type = (String) fieldSchema.get("type");
        String format = (String) fieldSchema.get("format");
        
        // 注释
        if (description != null && !description.isEmpty()) {
            sb.append("    /**\n");
            sb.append("     * ").append(description).append("\n");
            sb.append("     */\n");
        }
        
        // FastJSON注解
        String camelCaseFieldName = toCamelCase(fieldName);
        if (!fieldName.equals(camelCaseFieldName)) {
            sb.append("    @JSONField(alternateNames = \"").append(fieldName).append("\")\n");
        }
        
        // 字段声明
        String javaType = mapToJavaType(type, format, fieldSchema);
        sb.append("    private ").append(javaType).append(" ").append(camelCaseFieldName).append(";\n\n");
    }
    
    /**
     * 将snake_case转换为camelCase
     */
    private static String toCamelCase(String snakeCase) {
        if (snakeCase == null || snakeCase.isEmpty()) {
            return snakeCase;
        }
        
        String[] parts = snakeCase.split("_");
        StringBuilder camelCase = new StringBuilder(parts[0]);
        
        for (int i = 1; i < parts.length; i++) {
            if (!parts[i].isEmpty()) {
                camelCase.append(Character.toUpperCase(parts[i].charAt(0)));
                if (parts[i].length() > 1) {
                    camelCase.append(parts[i].substring(1));
                }
            }
        }
        
        return camelCase.toString();
    }
    
    /**
     * 映射到Java类型
     */
    private static String mapToJavaType(String type, String format, Map<String, Object> schema) {
        // 处理$ref引用
        if (schema.containsKey("$ref")) {
            String ref = (String) schema.get("$ref");
            return extractClassNameFromRef(ref);
        }

        if (type == null) return "Object";

        switch (type) {
            case "string":
                if ("date-time".equals(format)) {
                    return "LocalDateTime";
                }
                return "String";
            case "integer":
                if ("int64".equals(format)) {
                    return "Long";
                }
                return "Integer";
            case "number":
                return "Double";
            case "boolean":
                return "Boolean";
            case "array":
                Map<String, Object> items = (Map<String, Object>) schema.get("items");
                if (items != null) {
                    // 处理数组项的$ref引用
                    if (items.containsKey("$ref")) {
                        String ref = (String) items.get("$ref");
                        String itemType = extractClassNameFromRef(ref);
                        return "List<" + itemType + ">";
                    }
                    String itemType = mapToJavaType((String) items.get("type"), (String) items.get("format"), items);
                    return "List<" + itemType + ">";
                }
                return "List<Object>";
            case "object":
                return "Map<String, Object>";
            default:
                return "Object";
        }
    }

    /**
     * 从$ref中提取类名
     */
    private static String extractClassNameFromRef(String ref) {
        if (ref != null && ref.startsWith("#/components/schemas/")) {
            return ref.substring("#/components/schemas/".length());
        }
        return "Object";
    }
    
    /**
     * 写入文件
     */
    private static void writeToFile(String className, String content, String packageName) throws Exception {
        String packagePath = packageName.replace(".", "/");
        Path dirPath = Paths.get(OUTPUT_DIR, packagePath);
        Files.createDirectories(dirPath);
        
        Path filePath = dirPath.resolve(className + ".java");
        
        // 检查文件是否存在
        if (Files.exists(filePath)) {
            System.out.print("文件 " + filePath + " 已存在，是否覆盖？(y/n): ");
            String answer = scanner.nextLine().trim().toLowerCase();
            if (!"y".equals(answer) && !"yes".equals(answer)) {
                System.out.println("跳过文件: " + className);
                return;
            }
        }
        
        Files.write(filePath, content.getBytes("UTF-8"));
        System.out.println("生成文件: " + filePath);
    }
    
    /**
     * 确定包名
     */
    private static String determinePackageName(ApiInfo apiInfo) {
        // 使用resource作为包名
        String resource = apiInfo.resource;
        if (resource == null || resource.isEmpty()) {
            resource = "common";
        }
        return "com.cloudpod.podsail.service.cloudpods.domain." + resource.toLowerCase().replaceAll("[^a-zA-Z0-9]", "");
    }
    
    /**
     * 提取服务名
     */
    private static String extractServiceName(ApiInfo apiInfo) {
        if (apiInfo.service != null && !apiInfo.service.isEmpty()) {
            return apiInfo.service;
        }
        
        // 从名称中提取
        if (apiInfo.name.contains(">")) {
            String[] parts = apiInfo.name.split(">");
            if (parts.length > 0) {
                return parts[0].trim().replaceAll("[()（）]", "");
            }
        }
        
        return "common";
    }
    
    /**
     * 获取网络内容
     */
    private static String fetchContent(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(30000);

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            return content.toString();
        }
    }

    /**
     * 解析API列表
     */
    private static List<ApiInfo> parseApiList(String content) {
        List<ApiInfo> apiList = new ArrayList<>();
        String[] lines = content.split("\n");

        Pattern pattern = Pattern.compile("- (.+?) > (.+?) \\[(.+?)\\]\\((.+?)\\): (.+)");

        for (String line : lines) {
            Matcher matcher = pattern.matcher(line);
            if (matcher.find()) {
                ApiInfo api = new ApiInfo();
                api.service = matcher.group(1);
                api.resource = matcher.group(2);
                api.name = matcher.group(3);
                api.url = matcher.group(4);
                apiList.add(api);
            }
        }

        return apiList;
    }

    /**
     * 按服务分组
     */
    private static Map<String, List<ApiInfo>> groupByService(List<ApiInfo> apiList) {
        Map<String, List<ApiInfo>> groups = new LinkedHashMap<>();

        for (ApiInfo api : apiList) {
            String service = api.service;
            groups.computeIfAbsent(service, k -> new ArrayList<>()).add(api);
        }

        return groups;
    }

    /**
     * 提取API ID
     */
    private static String extractApiId(String url) {
        Pattern pattern = Pattern.compile("api-(\\d+)\\.md");
        Matcher matcher = pattern.matcher(url);
        return matcher.find() ? matcher.group(1) : null;
    }

    /**
     * 提取OpenAPI规范
     */
    private static String extractOpenApiSpec(String content) {
        Pattern pattern = Pattern.compile("```yaml\\s*\\n(.*?)\\n```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(content);
        return matcher.find() ? matcher.group(1) : null;
    }

    /**
     * 搜索API接口
     */
    private static List<ApiInfo> searchApis(List<ApiInfo> apiList, String keyword) {
        List<ApiInfo> matchedApis = new ArrayList<>();
        String lowerKeyword = keyword.toLowerCase();

        for (ApiInfo api : apiList) {
            if (api.name.toLowerCase().contains(lowerKeyword) ||
                api.service.toLowerCase().contains(lowerKeyword) ||
                api.resource.toLowerCase().contains(lowerKeyword)) {
                matchedApis.add(api);
            }
        }

        return matchedApis;
    }

    /**
     * 选择并处理API接口
     */
    private static void selectAndProcessApis(List<ApiInfo> matchedApis) throws Exception {
        System.out.println("\n找到 " + matchedApis.size() + " 个匹配的接口:");

        // 显示搜索结果
        for (int i = 0; i < matchedApis.size(); i++) {
            ApiInfo api = matchedApis.get(i);
            System.out.printf("%d. [%s] %s > %s - %s\n",
                i + 1, api.service, api.resource, api.name, api.url);
        }

        System.out.println("\n选择方式:");
        System.out.println("1. 输入单个数字 (如: 1)");
        System.out.println("2. 输入多个数字，用逗号分隔 (如: 1,3,5)");
        System.out.println("3. 输入范围 (如: 1-5)");
        System.out.println("4. 输入 'all' 选择全部");
        System.out.print("请选择: ");

        String selection = scanner.nextLine().trim();

        if (selection.isEmpty()) {
            System.out.println("未选择任何接口");
            return;
        }

        List<ApiInfo> selectedApis = parseSelection(matchedApis, selection);

        if (selectedApis.isEmpty()) {
            System.out.println("选择无效或为空");
            return;
        }

        System.out.println("\n将处理 " + selectedApis.size() + " 个接口:");
        for (ApiInfo api : selectedApis) {
            System.out.println("- " + api.name);
        }

        System.out.print("确认处理？(y/n): ");
        String confirm = scanner.nextLine().trim().toLowerCase();

        if ("y".equals(confirm) || "yes".equals(confirm)) {
            for (ApiInfo api : selectedApis) {
                try {
                    processApi(api);
                } catch (Exception e) {
                    System.err.println("处理接口失败: " + api.name + " - " + e.getMessage());
                }
            }
            System.out.println("选中的接口处理完成！");
        } else {
            System.out.println("已取消处理");
        }
    }

    /**
     * 解析用户选择
     */
    private static List<ApiInfo> parseSelection(List<ApiInfo> matchedApis, String selection) {
        List<ApiInfo> selectedApis = new ArrayList<>();

        if ("all".equalsIgnoreCase(selection)) {
            return new ArrayList<>(matchedApis);
        }

        try {
            if (selection.contains("-")) {
                // 处理范围选择 (如: 1-5)
                String[] parts = selection.split("-");
                if (parts.length == 2) {
                    int start = Integer.parseInt(parts[0].trim()) - 1;
                    int end = Integer.parseInt(parts[1].trim()) - 1;

                    if (start >= 0 && end < matchedApis.size() && start <= end) {
                        for (int i = start; i <= end; i++) {
                            selectedApis.add(matchedApis.get(i));
                        }
                    }
                }
            } else if (selection.contains(",")) {
                // 处理多个选择 (如: 1,3,5)
                String[] parts = selection.split(",");
                for (String part : parts) {
                    int index = Integer.parseInt(part.trim()) - 1;
                    if (index >= 0 && index < matchedApis.size()) {
                        selectedApis.add(matchedApis.get(index));
                    }
                }
            } else {
                // 处理单个选择
                int index = Integer.parseInt(selection) - 1;
                if (index >= 0 && index < matchedApis.size()) {
                    selectedApis.add(matchedApis.get(index));
                }
            }
        } catch (NumberFormatException e) {
            System.out.println("选择格式错误");
        }

        return selectedApis;
    }

    /**
     * API信息类
     */
    static class ApiInfo {
        String name;
        String url;
        String service;
        String resource;
    }
}
