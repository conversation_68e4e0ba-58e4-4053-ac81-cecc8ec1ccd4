package com.cloudpod.podsail.service.cloudpods.serversku;

import com.cloudpod.podsail.service.cloudpods.serversku.dto.*;

/**
 * CloudPods 计算服务 ServerSku 相关服务接口
 * 主机套餐规格管理服务
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
public interface CloudPodsServerSkuService {

    /**
     * 获取主机套餐规格列表
     *
     * @param queryRequest 查询参数
     * @return 套餐规格列表
     */
    CloudPodsServerSkuListResponse getServerSkuList(CloudPodsServerSkuQueryRequest queryRequest);

    /**
     * 获取主机套餐规格详情
     *
     * @param skuId 套餐规格ID
     * @return 套餐规格详情
     */
    CloudPodsServerSkuDetailResponse getServerSkuDetail(String skuId);

    /**
     * 创建主机套餐规格
     *
     * @param createRequest 创建请求参数
     * @return 创建结果
     */
    CloudPodsServerSkuCreateResponse createServerSku(CloudPodsServerSkuCreateRequest createRequest);

    /**
     * 更新主机套餐规格
     *
     * @param skuId 套餐规格ID
     * @param updateRequest 更新请求参数
     * @return 更新结果
     */
    CloudPodsServerSkuUpdateResponse updateServerSku(String skuId, CloudPodsServerSkuUpdateRequest updateRequest);

    /**
     * 删除主机套餐规格
     *
     * @param skuId 套餐规格ID
     * @return 删除结果
     */
    CloudPodsServerSkuDeleteResponse deleteServerSku(String skuId);

    /**
     * 启用主机套餐规格
     *
     * @param skuId 套餐规格ID
     * @return 操作结果
     */
    CloudPodsServerSkuOperationResponse enableServerSku(String skuId);

    /**
     * 禁用主机套餐规格
     *
     * @param skuId 套餐规格ID
     * @return 操作结果
     */
    CloudPodsServerSkuOperationResponse disableServerSku(String skuId);

    /**
     * 获取实例规格配置
     *
     * @param queryRequest 查询参数
     * @return 实例规格配置
     */
    CloudPodsServerSkuInstanceSpecsResponse getInstanceSpecs(CloudPodsServerSkuInstanceSpecsQueryRequest queryRequest);

    /**
     * 获取指定字段的不重复值
     *
     * @param queryRequest 查询参数
     * @return 字段不重复值列表
     */
    CloudPodsServerSkuDistinctFieldResponse getDistinctField(CloudPodsServerSkuDistinctFieldQueryRequest queryRequest);

    /**
     * 获取套餐规格统计信息
     *
     * @param queryRequest 查询参数
     * @return 统计信息
     */
    CloudPodsServerSkuStatisticsResponse getStatistics(CloudPodsServerSkuStatisticsQueryRequest queryRequest);
}
