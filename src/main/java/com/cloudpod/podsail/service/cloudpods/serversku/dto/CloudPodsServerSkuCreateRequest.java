package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * CloudPods ServerSku 创建请求参数
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuCreateRequest {

    /**
     * 套餐规格名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * CPU核数
     */
    @JSONField(name = "cpu_core_count")
    private Integer cpuCoreCount;

    /**
     * 内存大小(MB)
     */
    @JSONField(name = "memory_size_mb")
    private Integer memorySizeMb;

    /**
     * 本地分类
     */
    @JSONField(name = "local_category")
    private String localCategory;

    /**
     * 实例类型族
     */
    @JSONField(name = "instance_type_family")
    private String instanceTypeFamily;

    /**
     * 实例类型
     */
    @JSONField(name = "instance_type")
    private String instanceType;

    /**
     * 云提供商
     */
    private String provider;

    /**
     * 云区域ID
     */
    @JSONField(name = "cloudregion_id")
    private String cloudregionId;

    /**
     * 可用区ID
     */
    @JSONField(name = "zone_id")
    private String zoneId;

    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 是否为公有云
     */
    @JSONField(name = "public_cloud")
    private Boolean publicCloud = false;

    /**
     * 后付费状态
     */
    @JSONField(name = "postpaid_status")
    private String postpaidStatus;

    /**
     * 预付费状态
     */
    @JSONField(name = "prepaid_status")
    private String prepaidStatus;

    /**
     * 项目ID
     */
    @JSONField(name = "project_id")
    private String projectId;

    /**
     * 域ID
     */
    @JSONField(name = "domain_id")
    private String domainId;

    /**
     * 虚拟化类型
     */
    private String hypervisor;

    /**
     * 操作系统架构
     */
    @JSONField(name = "os_arch")
    private String osArch;

    /**
     * GPU数量
     */
    @JSONField(name = "gpu_count")
    private Integer gpuCount;

    /**
     * GPU规格
     */
    @JSONField(name = "gpu_spec")
    private String gpuSpec;

    /**
     * 网络最大带宽
     */
    @JSONField(name = "network_max_bandwidth")
    private Integer networkMaxBandwidth;

    /**
     * 网络最大连接数
     */
    @JSONField(name = "network_max_connections")
    private Integer networkMaxConnections;

    /**
     * 存储类型
     */
    @JSONField(name = "storage_type")
    private String storageType;

    /**
     * 数据盘类型
     */
    @JSONField(name = "data_disk_types")
    private String dataDiskTypes;

    /**
     * 数据盘最大数量
     */
    @JSONField(name = "data_disk_max_count")
    private Integer dataDiskMaxCount;

    /**
     * 数据盘最大容量
     */
    @JSONField(name = "data_disk_max_size_gb")
    private Integer dataDiskMaxSizeGb;

    /**
     * 系统盘最小容量
     */
    @JSONField(name = "sys_disk_min_size_gb")
    private Integer sysDiskMinSizeGb;

    /**
     * 系统盘最大容量
     */
    @JSONField(name = "sys_disk_max_size_gb")
    private Integer sysDiskMaxSizeGb;

    /**
     * 系统盘类型
     */
    @JSONField(name = "sys_disk_type")
    private String sysDiskType;

    /**
     * 是否支持热插拔
     */
    @JSONField(name = "hotplug_cpu_mem")
    private Boolean hotplugCpuMem;

    /**
     * 标签
     */
    private Map<String, String> tags;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 扩展属性
     */
    private Map<String, Object> extendedProperties;
}
