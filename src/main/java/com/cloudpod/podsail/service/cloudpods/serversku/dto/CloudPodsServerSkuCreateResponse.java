package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 创建响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuCreateResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 创建的套餐规格ID
     */
    private String serverSkuId;

    /**
     * 创建的套餐规格名称
     */
    private String serverSkuName;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 创建成功的静态方法
     */
    public static CloudPodsServerSkuCreateResponse success(String serverSkuId, String serverSkuName, String responseData) {
        return new CloudPodsServerSkuCreateResponse()
                .setSuccess(true)
                .setServerSkuId(serverSkuId)
                .setServerSkuName(serverSkuName)
                .setResponseData(responseData);
    }

    /**
     * 创建失败的静态方法
     */
    public static CloudPodsServerSkuCreateResponse failure(String errorMessage) {
        return new CloudPodsServerSkuCreateResponse()
                .setSuccess(false)
                .setErrorMessage(errorMessage);
    }
}
