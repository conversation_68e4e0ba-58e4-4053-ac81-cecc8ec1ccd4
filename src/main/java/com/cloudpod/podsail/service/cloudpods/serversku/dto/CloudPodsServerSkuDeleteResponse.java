package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 删除响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuDeleteResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 删除的套餐规格ID
     */
    private String serverSkuId;

    /**
     * 删除的套餐规格名称
     */
    private String serverSkuName;

    /**
     * 删除时间
     */
    private String deletedAt;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 删除成功的静态方法
     */
    public static CloudPodsServerSkuDeleteResponse success(String serverSkuId, String serverSkuName, String responseData) {
        return new CloudPodsServerSkuDeleteResponse()
                .setSuccess(true)
                .setServerSkuId(serverSkuId)
                .setServerSkuName(serverSkuName)
                .setResponseData(responseData);
    }

    /**
     * 删除失败的静态方法
     */
    public static CloudPodsServerSkuDeleteResponse failure(String errorMessage) {
        return new CloudPodsServerSkuDeleteResponse()
                .setSuccess(false)
                .setErrorMessage(errorMessage);
    }
}
