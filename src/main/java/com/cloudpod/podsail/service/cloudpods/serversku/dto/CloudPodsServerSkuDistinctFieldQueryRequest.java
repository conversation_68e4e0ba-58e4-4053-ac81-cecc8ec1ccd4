package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 字段去重查询请求参数
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuDistinctFieldQueryRequest {

    /**
     * 要查询的字段名
     */
    private String field;

    /**
     * 是否为公有云
     */
    @JSONField(name = "public_cloud")
    private Boolean publicCloud;

    /**
     * 后付费状态
     */
    @JSONField(name = "postpaid_status")
    private String postpaidStatus;

    /**
     * 预付费状态
     */
    private String prepaidStatus;

    /**
     * CPU核数
     */
    private Integer cpuCoreCount;

    /**
     * 内存大小(MB)
     */
    private Integer memorySizeMb;

    /**
     * 云区域
     */
    private String cloudregion;

    /**
     * 云提供商
     */
    private String provider;

    /**
     * 作用域
     */
    private String scope;

    /**
     * 本地分类
     */
    private String localCategory;

    /**
     * 虚拟化类型
     */
    private String hypervisor;

    /**
     * 操作系统架构
     */
    private String osArch;

    /**
     * 可用区
     */
    private String zone;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 域ID
     */
    private String domainId;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否可用
     */
    private Boolean usable;

    /**
     * 存储类型
     */
    private String storageType;

    /**
     * GPU数量
     */
    private Integer gpuCount;

    /**
     * 网络类型
     */
    private String networkType;

    /**
     * 实例类型族
     */
    private String instanceTypeFamily;

    /**
     * 实例类型
     */
    private String instanceType;

    /**
     * 状态
     */
    private String status;

    /**
     * 搜索关键词
     */
    private String search;

    /**
     * 过滤条件
     */
    private String filter;
}
