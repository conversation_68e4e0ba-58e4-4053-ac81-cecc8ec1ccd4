package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods ServerSku 字段去重响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuDistinctFieldResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 字段名
     */
    private String field;

    /**
     * 字段值列表
     */
    private List<String> values;

    /**
     * 字段值详细信息列表
     */
    private List<FieldValueInfo> valueInfos;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 字段值详细信息
     */
    @Data
    @Accessors(chain = true)
    public static class FieldValueInfo {

        /**
         * 字段值
         */
        private String value;

        /**
         * 字段值显示名称
         */
        private String displayName;

        /**
         * 字段值描述
         */
        private String description;

        /**
         * 该值对应的记录数量
         */
        private Integer count;

        /**
         * 是否可用
         */
        private Boolean available;

        /**
         * 排序权重
         */
        private Integer sortWeight;

        /**
         * 扩展属性
         */
        private Object extendedInfo;
    }
}
