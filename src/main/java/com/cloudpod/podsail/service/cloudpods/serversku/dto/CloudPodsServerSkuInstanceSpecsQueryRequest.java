package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 实例规格查询请求参数
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuInstanceSpecsQueryRequest {

    /**
     * 是否可用
     */
    private Boolean usable;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 云区域
     */
    private String cloudregion;

    /**
     * 云提供商
     */
    private String provider;

    /**
     * 是否为公有云
     */
    @JSONField(name = "public_cloud")
    private Boolean publicCloud;

    /**
     * 后付费状态
     */
    @JSONField(name = "postpaid_status")
    private String postpaidStatus;

    /**
     * 预付费状态
     */
    @JSONField(name = "prepaid_status")
    private String prepaidStatus;

    /**
     * CPU核数
     */
    @JSONField(name = "cpu_core_count")
    private Integer cpuCoreCount;

    /**
     * 内存大小(MB)
     */
    @JSONField(name = "memory_size_mb")
    private Integer memorySizeMb;

    /**
     * 本地分类
     */
    @JSONField(name = "local_category")
    private String localCategory;

    /**
     * 虚拟化类型
     */
    private String hypervisor;

    /**
     * 操作系统架构
     */
    @JSONField(name = "os_arch")
    private String osArch;

    /**
     * 可用区
     */
    private String zone;

    /**
     * 项目ID
     */
    @JSONField(name = "project_id")
    private String projectId;

    /**
     * 域ID
     */
    @JSONField(name = "domain_id")
    private String domainId;

    /**
     * 作用域
     */
    private String scope;

    /**
     * 存储类型
     */
    @JSONField(name = "storage_type")
    private String storageType;

    /**
     * GPU数量
     */
    @JSONField(name = "gpu_count")
    private Integer gpuCount;

    /**
     * 网络类型
     */
    @JSONField(name = "network_type")
    private String networkType;

    /**
     * 是否支持热插拔
     */
    @JSONField(name = "hotplug_cpu_mem")
    private Boolean hotplugCpuMem;

    /**
     * 实例类型族
     */
    @JSONField(name = "instance_type_family")
    private String instanceTypeFamily;

    /**
     * 实例类型
     */
    @JSONField(name = "instance_type")
    private String instanceType;
}
