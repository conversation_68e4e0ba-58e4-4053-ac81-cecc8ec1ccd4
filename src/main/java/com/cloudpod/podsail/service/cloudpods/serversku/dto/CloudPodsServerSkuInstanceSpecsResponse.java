package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * CloudPods ServerSku 实例规格响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuInstanceSpecsResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * CPU规格列表
     */
    private List<Integer> cpuSpecs;

    /**
     * 内存规格列表
     */
    private List<Integer> memorySpecs;

    /**
     * 套餐规格组合
     */
    private List<InstanceSpec> instanceSpecs;

    /**
     * 存储类型信息
     */
    private Map<String, StorageTypeInfo> storageTypes;

    /**
     * 网络类型信息
     */
    private Map<String, NetworkTypeInfo> networkTypes;

    /**
     * 可用性信息
     */
    private AvailabilityInfo availabilityInfo;

    /**
     * 实例规格信息
     */
    @Data
    @Accessors(chain = true)
    public static class InstanceSpec {

        /**
         * CPU核数
         */
        private Integer cpuCoreCount;

        /**
         * 内存大小(MB)
         */
        private Integer memorySizeMb;

        /**
         * 本地分类
         */
        private String localCategory;

        /**
         * 实例类型
         */
        private String instanceType;

        /**
         * 是否可用
         */
        private Boolean available;

        /**
         * 价格信息
         */
        private PriceInfo priceInfo;

        /**
         * 容量信息
         */
        private CapacityInfo capacityInfo;
    }

    /**
     * 存储类型信息
     */
    @Data
    @Accessors(chain = true)
    public static class StorageTypeInfo {

        /**
         * 存储类型名称
         */
        private String name;

        /**
         * 存储类型描述
         */
        private String description;

        /**
         * 总容量
         */
        private Long totalCapacity;

        /**
         * 可用容量
         */
        private Long availableCapacity;

        /**
         * 已用容量
         */
        private Long usedCapacity;

        /**
         * 是否为系统盘存储
         */
        private Boolean isSysDiskStore;

        /**
         * 存储列表
         */
        private List<StorageInfo> storages;
    }

    /**
     * 存储信息
     */
    @Data
    @Accessors(chain = true)
    public static class StorageInfo {

        /**
         * 存储ID
         */
        private String id;

        /**
         * 存储名称
         */
        private String name;

        /**
         * 容量
         */
        private Long capacity;

        /**
         * 可用容量
         */
        private Long availableCapacity;
    }

    /**
     * 网络类型信息
     */
    @Data
    @Accessors(chain = true)
    public static class NetworkTypeInfo {

        /**
         * 网络类型名称
         */
        private String name;

        /**
         * 网络类型描述
         */
        private String description;

        /**
         * 最大带宽
         */
        private Integer maxBandwidth;

        /**
         * 最大连接数
         */
        private Integer maxConnections;

        /**
         * 是否可用
         */
        private Boolean available;
    }

    /**
     * 价格信息
     */
    @Data
    @Accessors(chain = true)
    public static class PriceInfo {

        /**
         * 按小时计费价格
         */
        private Double hourlyPrice;

        /**
         * 按月计费价格
         */
        private Double monthlyPrice;

        /**
         * 按年计费价格
         */
        private Double yearlyPrice;

        /**
         * 货币单位
         */
        private String currency;
    }

    /**
     * 容量信息
     */
    @Data
    @Accessors(chain = true)
    public static class CapacityInfo {

        /**
         * 总容量
         */
        private Integer totalCapacity;

        /**
         * 已用容量
         */
        private Integer usedCapacity;

        /**
         * 可用容量
         */
        private Integer availableCapacity;
    }

    /**
     * 可用性信息
     */
    @Data
    @Accessors(chain = true)
    public static class AvailabilityInfo {

        /**
         * 是否可用
         */
        private Boolean usable;

        /**
         * 不可用原因
         */
        private String unavailableReason;

        /**
         * 可用区域列表
         */
        private List<String> availableZones;

        /**
         * 不可用区域列表
         */
        private List<String> unavailableZones;
    }
}
