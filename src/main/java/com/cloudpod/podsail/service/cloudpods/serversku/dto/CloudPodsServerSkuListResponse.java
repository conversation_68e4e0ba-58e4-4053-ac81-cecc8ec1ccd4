package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods ServerSku 列表响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuListResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 分页限制
     */
    private Integer limit;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 套餐规格列表
     */
    private List<ServerSkuSummary> serverSkus;

    /**
     * 套餐规格摘要信息
     */
    @Data
    @Accessors(chain = true)
    public static class ServerSkuSummary {

        /**
         * 套餐规格ID
         */
        private String id;

        /**
         * 套餐规格名称
         */
        private String name;

        /**
         * 描述
         */
        private String description;

        /**
         * CPU核数
         */
        @JSONField(name = "cpu_core_count")
        private Integer cpuCoreCount;

        /**
         * 内存大小(MB)
         */
        @JSONField(name = "memory_size_mb")
        private Integer memorySizeMb;

        /**
         * 本地分类
         */
        @JSONField(name = "local_category")
        private String localCategory;

        /**
         * 实例类型族
         */
        @JSONField(name = "instance_type_family")
        private String instanceTypeFamily;

        /**
         * 实例类型
         */
        @JSONField(name = "instance_type")
        private String instanceType;

        /**
         * 云提供商
         */
        private String provider;

        /**
         * 云区域
         */
        private String cloudregion;

        /**
         * 云区域ID
         */
        @JSONField(name = "cloudregion_id")
        private String cloudregionId;

        /**
         * 可用区
         */
        private String zone;

        /**
         * 可用区ID
         */
        @JSONField(name = "zone_id")
        private String zoneId;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 状态
         */
        private String status;

        /**
         * 是否为公有云
         */
        @JSONField(name = "public_cloud")
        private Boolean publicCloud;

        /**
         * 后付费状态
         */
        @JSONField(name = "postpaid_status")
        private String postpaidStatus;

        /**
         * 预付费状态
         */
        @JSONField(name = "prepaid_status")
        private String prepaidStatus;

        /**
         * 创建时间
         */
        @JSONField(name = "created_at")
        private String createdAt;

        /**
         * 更新时间
         */
        private String updatedAt;

        /**
         * 项目ID
         */
        private String projectId;

        /**
         * 项目名称
         */
        private String project;

        /**
         * 域ID
         */
        private String domainId;

        /**
         * 域名称
         */
        private String domain;

        /**
         * 虚拟化类型
         */
        private String hypervisor;

        /**
         * 操作系统架构
         */
        private String osArch;

        /**
         * GPU数量
         */
        private Integer gpuCount;

        /**
         * GPU规格
         */
        private String gpuSpec;

        /**
         * 网络最大带宽
         */
        private Integer networkMaxBandwidth;

        /**
         * 网络最大连接数
         */
        private Integer networkMaxConnections;

        /**
         * 存储类型
         */
        private String storageType;

        /**
         * 数据盘类型
         */
        private String dataDiskTypes;

        /**
         * 数据盘最大数量
         */
        private Integer dataDiskMaxCount;

        /**
         * 数据盘最大容量
         */
        private Integer dataDiskMaxSizeGb;

        /**
         * 系统盘最小容量
         */
        private Integer sysDiskMinSizeGb;

        /**
         * 系统盘最大容量
         */
        private Integer sysDiskMaxSizeGb;

        /**
         * 系统盘类型
         */
        private String sysDiskType;

        /**
         * 是否支持热插拔
         */
        private Boolean hotplugCpuMem;

        /**
         * 标签
         */
        private String tags;

        /**
         * 元数据
         */
        private String metadata;
    }
}
