package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 操作响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuOperationResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 操作类型
     */
    private String operation;

    /**
     * 套餐规格ID
     */
    private String serverSkuId;

    /**
     * 套餐规格名称
     */
    private String serverSkuName;

    /**
     * 操作时间
     */
    private String operationTime;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 操作成功的静态方法
     */
    public static CloudPodsServerSkuOperationResponse success(String operation, String serverSkuId, String responseData) {
        return new CloudPodsServerSkuOperationResponse()
                .setSuccess(true)
                .setOperation(operation)
                .setServerSkuId(serverSkuId)
                .setResponseData(responseData);
    }

    /**
     * 操作失败的静态方法
     */
    public static CloudPodsServerSkuOperationResponse failure(String operation, String errorMessage) {
        return new CloudPodsServerSkuOperationResponse()
                .setSuccess(false)
                .setOperation(operation)
                .setErrorMessage(errorMessage);
    }
}
