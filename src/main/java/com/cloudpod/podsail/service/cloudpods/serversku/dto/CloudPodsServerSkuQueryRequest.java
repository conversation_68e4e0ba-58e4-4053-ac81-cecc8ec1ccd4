package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods ServerSku 查询请求参数
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuQueryRequest {
    
    /**
     * 分页限制
     */
    private Integer limit = 20;
    
    /**
     * 分页偏移量
     */
    private Integer offset = 0;
    
    /**
     * 列表排序时，用于排序的字段的名称，该字段不提供时，则按默认字段排序。一般时按照资源的新建时间逆序排序。
     */
    @JSONField(alternateNames = "order_by")
    private List<String> orderBy;
    
    /**
     * 列表排序时的顺序，desc为从高到低，asc为从低到高。默认是按照资源的创建时间desc排序。
     */
    private String order;
    
    /**
     * 是否返回详细信息
     */
    private String details;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 过滤条件
     */
    private List<String> filter;
    
    /**
     * 过滤条件
     */
    @JSONField(alternateNames = "joint_filter")
    private List<String> jointFilter;
    
    /**
     * 如果filter_any为true，则查询所有filter的并集，否则为交集
     */
    @JSONField(alternateNames = "filter_any")
    private String filterAny;
    
    /**
     * 返回结果只包含指定的字段
     */
    private List<String> field;
    
    /**
     * 用于数据导出，指定导出的数据字段
     */
    @JSONField(alternateNames = "export_keys")
    private String exportKeys;
    
    /**
     * 返回结果携带delete_fail_reason和update_fail_reason字段
     */
    @JSONField(alternateNames = "show_fail_reason")
    private String showFailReason;
    
    /**
     * 是否返回状态统计信息，默认为False
     */
    @JSONField(alternateNames = "summary_stats")
    private String summaryStats;
    
    /**
     * 按照标签排序
     */
    @JSONField(alternateNames = "order_by_tag")
    private String orderByTag;
    
    /**
     * 是否返回用户元数据
     */
    @JSONField(alternateNames = "with_user_meta")
    private String withUserMeta;
    
    /**
     * 是否不返回用户元数据
     */
    @JSONField(alternateNames = "without_user_meta")
    private String withoutUserMeta;
    
    /**
     * 返回包含外部标签的资源
     */
    @JSONField(alternateNames = "with_cloud_meta")
    private String withCloudMeta;
    
    /**
     * 返回包含任意标签的资源
     */
    @JSONField(alternateNames = "with_any_meta")
    private String with_any_meta;
    
    /**
     * 返回列表数据中包含资源的标签数据（Metadata）
     */
    @JSONField(alternateNames = "with_meta")
    private String withMeta;
    /**
     * 显示所有的资源，包括模拟的资源
     */
    @JSONField(alternateNames = "show_emulated")
    private String showEmulated;
    
    /**
     * 以资源是否启用/禁用过滤列表
     */
    private String enabled;
    
    /**
     * 指定查询的权限范围，可能值为project, domain or system
     */
    private String scope;
    
    /**
     * 指定项目归属域名称或ID
     */
    @JSONField(alternateNames = "project_domain_id")
    private String projectDomainId;
    
    /**
     * 对具有域属性的资源，严格匹配域ID
     */
    @JSONField(alternateNames = "project_domain_ids")
    private List<String> projectDomainIds;
    
    /**
     * 按domain名称排序，可能值为asc|desc
     */
    @JSONField(alternateNames = "order_by_domain")
    private String orderByDomain;
    
    /**
     * 列出指定云平台的资源，支持的云平台如下:
     * <br/>
     * Provider	开始支持版本	平台
     * OneCloud	0.0	OneCloud内置私有云，包括KVM和裸金属管理
     * VMware	1.2	VMware vCenter
     * OpenStack	2.6	OpenStack M版本以上私有云
     * ZStack	2.10	ZStack私有云
     * Aliyun	2.0	阿里云
     * Aws	2.3	Amazon AWS
     * Azure	2.2	Microsoft Azure
     * Google	2.13	Google Cloud Platform
     * Qcloud	2.3	腾讯云
     * Huawei	2.5	华为公有云
     * Ucloud	2.7	UCLOUD
     * Ctyun	2.13	天翼云
     * S3	2.11	通用s3对象存储
     * Ceph	2.11	Ceph对象存储
     * Xsky	2.11	XSKY启明星辰Ceph对象存储
     */
    private List<String> providers;
    
    /**
     * 指定云平台品牌
     */
    private List<String> brands;
    
    /**
     * 云环境，列出指定云环境的资源，支持云环境如下：
     * public	公有云
     * private	私有云
     * onpremise	本地IDC
     */
    @JSONField(name = "cloud_env")
    private String cloudEnv;
    
    /**
     * 以平台名称排序
     */
    @JSONField(alternateNames = "order_by_provider")
    private String orderByProvider;
    
    /**
     * 以平台品牌排序
     */
    @JSONField(alternateNames = "order_by_brand")
    private String orderByBrand;
    
    /**
     * 列出关联指定云订阅(ID或Name)的资源
     */
    @JSONField(alternateNames = "cloudprovider_id")
    private List<String> cloudproviderId;
    
    /**
     * 列出关联指定云账号(ID或Name)的资源
     */
    @JSONField(alternateNames = "cloudaccount_id")
    private List<String> cloudaccountId;
    
    /**
     * 过滤资源，是否为非OneCloud内置私有云管理的资源
     */
    @JSONField(alternateNames = "is_managed")
    private String isManaged;
    
    /**
     * 以云账号名称排序
     */
    @JSONField(alternateNames = "order_by_account")
    private String orderByAccount;
    
    /**
     * 以云订阅名称排序
     */
    @JSONField(alternateNames = "order_by_manager")
    private String orderByManager;
    
    /**
     * 区域名称或ID
     */
    @JSONField(alternateNames = "cloudregion_id")
    private String cloudregionId;
    
    /**
     * 过滤位于指定城市区域的资源
     */
    private String city;
    
}
