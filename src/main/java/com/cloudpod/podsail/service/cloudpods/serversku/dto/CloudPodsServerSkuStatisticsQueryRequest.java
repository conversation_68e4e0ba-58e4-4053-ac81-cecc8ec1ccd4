package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 统计查询请求参数
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuStatisticsQueryRequest {

    /**
     * 云区域
     */
    private String cloudregion;

    /**
     * 云提供商
     */
    private String provider;

    /**
     * 是否为公有云
     */
    private Boolean publicCloud;

    /**
     * 虚拟化类型
     */
    private String hypervisor;

    /**
     * 操作系统架构
     */
    private String osArch;

    /**
     * 可用区
     */
    private String zone;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 域ID
     */
    private String domainId;

    /**
     * 作用域
     */
    private String scope;

    /**
     * 本地分类
     */
    private String localCategory;

    /**
     * 实例类型族
     */
    private String instanceTypeFamily;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否可用
     */
    private Boolean usable;

    /**
     * 后付费状态
     */
    private String postpaidStatus;

    /**
     * 预付费状态
     */
    private String prepaidStatus;

    /**
     * 状态
     */
    private String status;

    /**
     * 统计类型 (count, capacity, usage等)
     */
    private String statisticsType;

    /**
     * 分组字段
     */
    private String groupBy;

    /**
     * 时间范围开始
     */
    private String timeRangeStart;

    /**
     * 时间范围结束
     */
    private String timeRangeEnd;

    /**
     * 搜索关键词
     */
    private String search;

    /**
     * 过滤条件
     */
    private String filter;
}
