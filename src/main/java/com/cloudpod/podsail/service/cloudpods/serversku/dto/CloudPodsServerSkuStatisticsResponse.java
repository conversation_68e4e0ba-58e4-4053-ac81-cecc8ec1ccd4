package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * CloudPods ServerSku 统计响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuStatisticsResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 统计类型
     */
    private String statisticsType;

    /**
     * 总体统计信息
     */
    private OverallStatistics overallStatistics;

    /**
     * 分组统计信息
     */
    private List<GroupStatistics> groupStatistics;

    /**
     * 时间序列统计信息
     */
    private List<TimeSeriesStatistics> timeSeriesStatistics;

    /**
     * 扩展统计信息
     */
    private Map<String, Object> extendedStatistics;

    /**
     * 总体统计信息
     */
    @Data
    @Accessors(chain = true)
    public static class OverallStatistics {

        /**
         * 总数量
         */
        private Integer totalCount;

        /**
         * 启用数量
         */
        private Integer enabledCount;

        /**
         * 禁用数量
         */
        private Integer disabledCount;

        /**
         * 可用数量
         */
        private Integer availableCount;

        /**
         * 不可用数量
         */
        private Integer unavailableCount;

        /**
         * 公有云数量
         */
        private Integer publicCloudCount;

        /**
         * 私有云数量
         */
        private Integer privateCloudCount;

        /**
         * 总CPU核数
         */
        private Long totalCpuCores;

        /**
         * 总内存大小(MB)
         */
        private Long totalMemoryMb;

        /**
         * 平均CPU核数
         */
        private Double averageCpuCores;

        /**
         * 平均内存大小(MB)
         */
        private Double averageMemoryMb;

        /**
         * 最小CPU核数
         */
        private Integer minCpuCores;

        /**
         * 最大CPU核数
         */
        private Integer maxCpuCores;

        /**
         * 最小内存大小(MB)
         */
        private Integer minMemoryMb;

        /**
         * 最大内存大小(MB)
         */
        private Integer maxMemoryMb;
    }

    /**
     * 分组统计信息
     */
    @Data
    @Accessors(chain = true)
    public static class GroupStatistics {

        /**
         * 分组字段
         */
        private String groupField;

        /**
         * 分组值
         */
        private String groupValue;

        /**
         * 分组显示名称
         */
        private String groupDisplayName;

        /**
         * 该分组的数量
         */
        private Integer count;

        /**
         * 该分组的百分比
         */
        private Double percentage;

        /**
         * 该分组的详细统计
         */
        private OverallStatistics detailStatistics;

        /**
         * 扩展信息
         */
        private Map<String, Object> extendedInfo;
    }

    /**
     * 时间序列统计信息
     */
    @Data
    @Accessors(chain = true)
    public static class TimeSeriesStatistics {

        /**
         * 时间点
         */
        private String timestamp;

        /**
         * 时间点显示名称
         */
        private String timeDisplayName;

        /**
         * 该时间点的数量
         */
        private Integer count;

        /**
         * 该时间点的详细统计
         */
        private OverallStatistics detailStatistics;

        /**
         * 扩展信息
         */
        private Map<String, Object> extendedInfo;
    }
}
