package com.cloudpod.podsail.service.cloudpods.serversku.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods ServerSku 更新响应
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Data
@Accessors(chain = true)
public class CloudPodsServerSkuUpdateResponse {

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 更新的套餐规格ID
     */
    private String serverSkuId;

    /**
     * 更新的套餐规格名称
     */
    private String serverSkuName;

    /**
     * 更新时间
     */
    private String updatedAt;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 更新成功的静态方法
     */
    public static CloudPodsServerSkuUpdateResponse success(String serverSkuId, String serverSkuName, String responseData) {
        return new CloudPodsServerSkuUpdateResponse()
                .setSuccess(true)
                .setServerSkuId(serverSkuId)
                .setServerSkuName(serverSkuName)
                .setResponseData(responseData);
    }

    /**
     * 更新失败的静态方法
     */
    public static CloudPodsServerSkuUpdateResponse failure(String errorMessage) {
        return new CloudPodsServerSkuUpdateResponse()
                .setSuccess(false)
                .setErrorMessage(errorMessage);
    }
}
