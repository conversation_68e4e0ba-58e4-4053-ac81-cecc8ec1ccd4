package com.cloudpod.podsail.service.cloudpods.serversku.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpod.podsail.config.CloudPodClientConfig;
import com.cloudpod.podsail.service.cloudpods.serversku.dto.*;
import com.cloudpod.podsail.service.cloudpods.serversku.CloudPodsServerSkuService;
import com.cloudpod.podsail.utils.JsonUtil;
import com.yunionyun.mcp.mcclient.AuthAgent;
import com.yunionyun.mcp.mcclient.Session;
import com.yunionyun.mcp.mcclient.EndpointType;
import com.yunionyun.mcp.mcclient.managers.ListResult;
import com.yunionyun.mcp.mcclient.managers.impl.compute.SkuManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CloudPods ServerSku 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Slf4j
@Service
public class CloudPodsServerSkuServiceImpl implements CloudPodsServerSkuService {
    
    @Autowired
    private AuthAgent authAgent;
    
    @Autowired
    private CloudPodClientConfig config;
    
    private Session getAdminSession() {
        return authAgent.getAdminSession(config.getDefaultRegion(), config.getDefaultZone(),
                EndpointType.ApigatewayURL);
    }
    
    @Override
    public CloudPodsServerSkuListResponse getServerSkuList(CloudPodsServerSkuQueryRequest queryRequest) {
        try {
            log.info("查询ServerSku列表: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildServerSkuQueryParams(queryRequest);
            log.info("CloudPods ServerSku查询参数: {}", queryParams.toJSONString());
            
            ListResult listResult = serverSkuManager.List(adminSession, queryParams);
            log.info("CloudPods查询ServerSku列表响应: 总数={}", listResult.getTotal());
            
            return parseServerSkuListResponse(listResult);
            
        } catch (Exception e) {
            log.error("查询ServerSku列表失败", e);
            return new CloudPodsServerSkuListResponse()
                    .setSuccess(false)
                    .setErrorMessage("查询ServerSku列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuDetailResponse getServerSkuDetail(String skuId) {
        try {
            log.info("获取ServerSku详情: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject params = new JSONObject();
            params.put("details", true);
            
            JSONObject result = serverSkuManager.Get(adminSession, skuId, params);
            log.info("获取ServerSku详情响应: {}", result.toJSONString());
            
            return parseServerSkuDetailResponse(result);
            
        } catch (Exception e) {
            log.error("获取ServerSku详情失败: {}", skuId, e);
            return new CloudPodsServerSkuDetailResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取ServerSku详情失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuCreateResponse createServerSku(CloudPodsServerSkuCreateRequest createRequest) {
        try {
            log.info("创建ServerSku请求: {}", createRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建创建参数
            JSONObject createParams = buildCreateServerSkuParams(createRequest);
            log.info("CloudPods创建ServerSku参数: {}", createParams.toJSONString());
            
            // 调用CloudPods API创建ServerSku
            JSONObject result = serverSkuManager.Create(adminSession, createParams);
            log.info("CloudPods创建ServerSku响应: {}", result.toJSONString());
            
            return parseCreateServerSkuResponse(result);
            
        } catch (Exception e) {
            log.error("创建ServerSku失败", e);
            return CloudPodsServerSkuCreateResponse.failure("创建ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuUpdateResponse updateServerSku(String skuId,
                                                            CloudPodsServerSkuUpdateRequest updateRequest) {
        try {
            log.info("更新ServerSku: {}, 请求: {}", skuId, updateRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建更新参数
            JSONObject updateParams = buildUpdateServerSkuParams(updateRequest);
            log.info("CloudPods更新ServerSku参数: {}", updateParams.toJSONString());
            
            JSONObject result = serverSkuManager.Update(adminSession, skuId, updateParams);
            log.info("CloudPods更新ServerSku响应: {}", result.toJSONString());
            
            return parseUpdateServerSkuResponse(result);
            
        } catch (Exception e) {
            log.error("更新ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuUpdateResponse.failure("更新ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuDeleteResponse deleteServerSku(String skuId) {
        try {
            log.info("删除ServerSku: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject result = serverSkuManager.Delete(adminSession, skuId);
            log.info("CloudPods删除ServerSku响应: {}", result.toJSONString());
            
            return parseDeleteServerSkuResponse(result, skuId);
            
        } catch (Exception e) {
            log.error("删除ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuDeleteResponse.failure("删除ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuOperationResponse enableServerSku(String skuId) {
        try {
            log.info("启用ServerSku: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject params = new JSONObject();
            JSONObject result = serverSkuManager.PerformAction(adminSession, skuId, "enable", params);
            
            log.info("启用ServerSku响应: {}", result.toJSONString());
            return CloudPodsServerSkuOperationResponse.success("enable", skuId, result.toJSONString());
            
        } catch (Exception e) {
            log.error("启用ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuOperationResponse.failure("enable", "启用ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuOperationResponse disableServerSku(String skuId) {
        try {
            log.info("禁用ServerSku: {}", skuId);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            JSONObject params = new JSONObject();
            JSONObject result = serverSkuManager.PerformAction(adminSession, skuId, "disable", params);
            
            log.info("禁用ServerSku响应: {}", result.toJSONString());
            return CloudPodsServerSkuOperationResponse.success("disable", skuId, result.toJSONString());
            
        } catch (Exception e) {
            log.error("禁用ServerSku失败: {}", skuId, e);
            return CloudPodsServerSkuOperationResponse.failure("disable", "禁用ServerSku失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuInstanceSpecsResponse getInstanceSpecs(
            CloudPodsServerSkuInstanceSpecsQueryRequest queryRequest) {
        try {
            log.info("获取实例规格配置: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildInstanceSpecsQueryParams(queryRequest);
            log.info("CloudPods实例规格查询参数: {}", queryParams.toJSONString());
            
            // 调用instance-specs接口
            JSONObject result = serverSkuManager.Get(adminSession, "instance-specs", queryParams);
            log.info("CloudPods实例规格响应: {}", result.toJSONString());
            
            return parseInstanceSpecsResponse(result);
            
        } catch (Exception e) {
            log.error("获取实例规格配置失败", e);
            return new CloudPodsServerSkuInstanceSpecsResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取实例规格配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuDistinctFieldResponse getDistinctField(
            CloudPodsServerSkuDistinctFieldQueryRequest queryRequest) {
        try {
            log.info("获取字段去重值: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildDistinctFieldQueryParams(queryRequest);
            log.info("CloudPods字段去重查询参数: {}", queryParams.toJSONString());
            
            // 调用distinct-field接口
            JSONObject result = serverSkuManager.Get(adminSession, "distinct-field", queryParams);
            log.info("CloudPods字段去重响应: {}", result.toJSONString());
            
            return parseDistinctFieldResponse(result, queryRequest.getField());
            
        } catch (Exception e) {
            log.error("获取字段去重值失败", e);
            return new CloudPodsServerSkuDistinctFieldResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取字段去重值失败: " + e.getMessage());
        }
    }
    
    @Override
    public CloudPodsServerSkuStatisticsResponse getStatistics(CloudPodsServerSkuStatisticsQueryRequest queryRequest) {
        try {
            log.info("获取ServerSku统计信息: {}", queryRequest);
            
            Session adminSession = getAdminSession();
            SkuManager serverSkuManager = new SkuManager();
            
            // 构建查询参数
            JSONObject queryParams = buildStatisticsQueryParams(queryRequest);
            log.info("CloudPods统计查询参数: {}", queryParams.toJSONString());
            
            // 调用statistics接口
            JSONObject result = serverSkuManager.Get(adminSession, "statistics", queryParams);
            log.info("CloudPods统计响应: {}", result.toJSONString());
            
            return parseStatisticsResponse(result);
            
        } catch (Exception e) {
            log.error("获取ServerSku统计信息失败", e);
            return new CloudPodsServerSkuStatisticsResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取ServerSku统计信息失败: " + e.getMessage());
        }
    }
    
    // ================== 私有方法 ==================
    
    /**
     * 构建ServerSku查询参数
     */
    private JSONObject buildServerSkuQueryParams(CloudPodsServerSkuQueryRequest request) {
        // 使用 FastJSON 的 toJSON 方法将对象转换为 JSONObject
        // 由于 DTO 类已经使用 @JSONField 注解处理字段名映射，直接转换即可
        return (JSONObject) JSON.toJSON(request);
    }
    
    /**
     * 解析ServerSku列表响应
     */
    private CloudPodsServerSkuListResponse parseServerSkuListResponse(ListResult listResult) {
        CloudPodsServerSkuListResponse response = new CloudPodsServerSkuListResponse()
                .setSuccess(true)
                .setTotal(listResult.getTotal())
                .setLimit(20) // 默认分页大小
                .setOffset(0); // 默认偏移量
        
        List<CloudPodsServerSkuListResponse.ServerSkuSummary> serverSkus = new ArrayList<>();
        if (listResult.getData() != null) {
            for (Object obj : listResult.getData()) {
                JSONObject serverSkuObj = (JSONObject) obj;
                CloudPodsServerSkuListResponse.ServerSkuSummary serverSku = parseServerSkuSummary(serverSkuObj);
                serverSkus.add(serverSku);
            }
        }
        response.setServerSkus(serverSkus);
        
        return response;
    }
    
    /**
     * 解析ServerSku摘要信息
     */
    private CloudPodsServerSkuListResponse.ServerSkuSummary parseServerSkuSummary(JSONObject serverSkuObj) {
        // 使用 FastJSON 的 parseObject 方法将 JSONObject 转换为 DTO 对象
        // 由于 DTO 类已经使用 @JSONField 注解处理字段名映射，直接转换即可
        return JSON.parseObject(serverSkuObj.toJSONString(), CloudPodsServerSkuListResponse.ServerSkuSummary.class);
    }
    
    /**
     * 解析ServerSku详情响应
     */
    private CloudPodsServerSkuDetailResponse parseServerSkuDetailResponse(JSONObject result) {
        CloudPodsServerSkuDetailResponse response = new CloudPodsServerSkuDetailResponse().setSuccess(true);

        // 使用 FastJSON 的 parseObject 方法将 JSONObject 转换为 DTO 对象
        // 由于 DTO 类已经使用 @JSONField 注解处理字段名映射，直接转换即可
        CloudPodsServerSkuDetailResponse.ServerSkuDetail serverSkuDetail =
                JSON.parseObject(result.toJSONString(), CloudPodsServerSkuDetailResponse.ServerSkuDetail.class);

        response.setServerSku(serverSkuDetail);
        return response;
    }
    
    /**
     * 构建创建ServerSku参数
     */
    private JSONObject buildCreateServerSkuParams(CloudPodsServerSkuCreateRequest request) {
        // 使用 FastJSON 的 toJSON 方法将对象转换为 JSONObject
        // 由于 DTO 类已经使用 @JSONField 注解处理字段名映射，直接转换即可
        return (JSONObject) JSON.toJSON(request);
    }
    
    /**
     * 解析创建ServerSku响应
     */
    private CloudPodsServerSkuCreateResponse parseCreateServerSkuResponse(JSONObject result) {
        String serverSkuId = result.getString("id");
        String serverSkuName = result.getString("name");
        return CloudPodsServerSkuCreateResponse.success(serverSkuId, serverSkuName, result.toJSONString());
    }
    
    /**
     * 构建更新ServerSku参数
     */
    private JSONObject buildUpdateServerSkuParams(CloudPodsServerSkuUpdateRequest request) {
        // 使用 FastJSON 的 toJSON 方法将对象转换为 JSONObject
        // 由于 DTO 类已经使用 @JSONField 注解处理字段名映射，直接转换即可
        return (JSONObject) JSON.toJSON(request);
    }
    
    /**
     * 解析更新ServerSku响应
     */
    private CloudPodsServerSkuUpdateResponse parseUpdateServerSkuResponse(JSONObject result) {
        String serverSkuId = result.getString("id");
        String serverSkuName = result.getString("name");
        return CloudPodsServerSkuUpdateResponse.success(serverSkuId, serverSkuName, result.toJSONString());
    }
    
    /**
     * 解析删除ServerSku响应
     */
    private CloudPodsServerSkuDeleteResponse parseDeleteServerSkuResponse(JSONObject result, String skuId) {
        String serverSkuName = result.getString("name");
        return CloudPodsServerSkuDeleteResponse.success(skuId, serverSkuName, result.toJSONString());
    }
    
    /**
     * 构建实例规格查询参数
     */
    private JSONObject buildInstanceSpecsQueryParams(CloudPodsServerSkuInstanceSpecsQueryRequest request) {
        // 使用 FastJSON 的 toJSON 方法将对象转换为 JSONObject
        // 由于 DTO 类已经使用 @JSONField 注解处理字段名映射，直接转换即可
        return (JSONObject) JSON.toJSON(request);
    }
    
    /**
     * 解析实例规格响应
     */
    private CloudPodsServerSkuInstanceSpecsResponse parseInstanceSpecsResponse(JSONObject result) {
        CloudPodsServerSkuInstanceSpecsResponse response = new CloudPodsServerSkuInstanceSpecsResponse()
                .setSuccess(true);
        
        // 解析CPU规格
        if (result.containsKey("cpu_specs") && result.get("cpu_specs") instanceof JSONArray) {
            List<Integer> cpuSpecs = new ArrayList<>();
            JSONArray cpuArray = result.getJSONArray("cpu_specs");
            for (int i = 0; i < cpuArray.size(); i++) {
                cpuSpecs.add(cpuArray.getInteger(i));
            }
            response.setCpuSpecs(cpuSpecs);
        }
        
        // 解析内存规格
        if (result.containsKey("memory_specs") && result.get("memory_specs") instanceof JSONArray) {
            List<Integer> memorySpecs = new ArrayList<>();
            JSONArray memoryArray = result.getJSONArray("memory_specs");
            for (int i = 0; i < memoryArray.size(); i++) {
                memorySpecs.add(memoryArray.getInteger(i));
            }
            response.setMemorySpecs(memorySpecs);
        }
        
        // 解析实例规格组合
        if (result.containsKey("instance_specs") && result.get("instance_specs") instanceof JSONArray) {
            List<CloudPodsServerSkuInstanceSpecsResponse.InstanceSpec> instanceSpecs = new ArrayList<>();
            JSONArray instanceArray = result.getJSONArray("instance_specs");
            for (int i = 0; i < instanceArray.size(); i++) {
                JSONObject instanceObj = instanceArray.getJSONObject(i);
                CloudPodsServerSkuInstanceSpecsResponse.InstanceSpec instanceSpec =
                        new CloudPodsServerSkuInstanceSpecsResponse.InstanceSpec()
                                .setCpuCoreCount(instanceObj.getInteger("cpu_core_count"))
                                .setMemorySizeMb(instanceObj.getInteger("memory_size_mb"))
                                .setLocalCategory(instanceObj.getString("local_category"))
                                .setInstanceType(instanceObj.getString("instance_type"))
                                .setAvailable(instanceObj.getBoolean("available"));
                instanceSpecs.add(instanceSpec);
            }
            response.setInstanceSpecs(instanceSpecs);
        }
        
        // 解析存储类型信息
        if (result.containsKey("storage_types") && result.get("storage_types") instanceof JSONObject) {
            Map<String, CloudPodsServerSkuInstanceSpecsResponse.StorageTypeInfo> storageTypes = new HashMap<>();
            JSONObject storageObj = result.getJSONObject("storage_types");
            for (String key : storageObj.keySet()) {
                JSONObject typeObj = storageObj.getJSONObject(key);
                CloudPodsServerSkuInstanceSpecsResponse.StorageTypeInfo storageTypeInfo =
                        new CloudPodsServerSkuInstanceSpecsResponse.StorageTypeInfo()
                                .setName(key)
                                .setDescription(typeObj.getString("description"))
                                .setTotalCapacity(typeObj.getLong("total_capacity"))
                                .setAvailableCapacity(typeObj.getLong("available_capacity"))
                                .setUsedCapacity(typeObj.getLong("used_capacity"))
                                .setIsSysDiskStore(typeObj.getBoolean("is_sys_disk_store"));
                storageTypes.put(key, storageTypeInfo);
            }
            response.setStorageTypes(storageTypes);
        }
        
        return response;
    }
    
    /**
     * 构建字段去重查询参数
     */
    private JSONObject buildDistinctFieldQueryParams(CloudPodsServerSkuDistinctFieldQueryRequest request) {
        // 使用 FastJSON 的 toJSON 方法将对象转换为 JSONObject
        // 注意：这个方法需要完善 DTO 类的 @JSONField 注解才能完全替代硬编码
        // 目前先使用 toJSON 方法，后续可以完善注解
        return (JSONObject) JSON.toJSON(request);
    }
    
    /**
     * 解析字段去重响应
     */
    private CloudPodsServerSkuDistinctFieldResponse parseDistinctFieldResponse(JSONObject result, String field) {
        CloudPodsServerSkuDistinctFieldResponse response = new CloudPodsServerSkuDistinctFieldResponse()
                .setSuccess(true)
                .setField(field);
        
        if (result.containsKey("data") && result.get("data") instanceof JSONArray) {
            List<String> values = new ArrayList<>();
            List<CloudPodsServerSkuDistinctFieldResponse.FieldValueInfo> valueInfos = new ArrayList<>();
            
            JSONArray dataArray = result.getJSONArray("data");
            for (int i = 0; i < dataArray.size(); i++) {
                if (dataArray.get(i) instanceof String) {
                    String value = dataArray.getString(i);
                    values.add(value);
                    
                    CloudPodsServerSkuDistinctFieldResponse.FieldValueInfo valueInfo =
                            new CloudPodsServerSkuDistinctFieldResponse.FieldValueInfo()
                                    .setValue(value)
                                    .setDisplayName(value)
                                    .setAvailable(true);
                    valueInfos.add(valueInfo);
                }
            }
            
            response.setValues(values);
            response.setValueInfos(valueInfos);
            response.setTotal(values.size());
        }
        
        return response;
    }
    
    /**
     * 构建统计查询参数
     */
    private JSONObject buildStatisticsQueryParams(CloudPodsServerSkuStatisticsQueryRequest request) {
        // 使用 FastJSON 的 toJSON 方法将对象转换为 JSONObject
        // 注意：这个方法需要完善 DTO 类的 @JSONField 注解才能完全替代硬编码
        // 目前先使用 toJSON 方法，后续可以完善注解
        return (JSONObject) JSON.toJSON(request);
    }
    
    /**
     * 解析统计响应
     */
    private CloudPodsServerSkuStatisticsResponse parseStatisticsResponse(JSONObject result) {
        CloudPodsServerSkuStatisticsResponse response = new CloudPodsServerSkuStatisticsResponse()
                .setSuccess(true);
        
        // 解析总体统计信息
        if (result.containsKey("overall") && result.get("overall") instanceof JSONObject) {
            JSONObject overallObj = result.getJSONObject("overall");
            CloudPodsServerSkuStatisticsResponse.OverallStatistics overallStats =
                    new CloudPodsServerSkuStatisticsResponse.OverallStatistics()
                            .setTotalCount(overallObj.getInteger("total_count"))
                            .setEnabledCount(overallObj.getInteger("enabled_count"))
                            .setDisabledCount(overallObj.getInteger("disabled_count"))
                            .setAvailableCount(overallObj.getInteger("available_count"))
                            .setUnavailableCount(overallObj.getInteger("unavailable_count"))
                            .setPublicCloudCount(overallObj.getInteger("public_cloud_count"))
                            .setPrivateCloudCount(overallObj.getInteger("private_cloud_count"))
                            .setTotalCpuCores(overallObj.getLong("total_cpu_cores"))
                            .setTotalMemoryMb(overallObj.getLong("total_memory_mb"))
                            .setAverageCpuCores(overallObj.getDouble("average_cpu_cores"))
                            .setAverageMemoryMb(overallObj.getDouble("average_memory_mb"))
                            .setMinCpuCores(overallObj.getInteger("min_cpu_cores"))
                            .setMaxCpuCores(overallObj.getInteger("max_cpu_cores"))
                            .setMinMemoryMb(overallObj.getInteger("min_memory_mb"))
                            .setMaxMemoryMb(overallObj.getInteger("max_memory_mb"));
            response.setOverallStatistics(overallStats);
        }
        
        // 解析分组统计信息
        if (result.containsKey("groups") && result.get("groups") instanceof JSONArray) {
            List<CloudPodsServerSkuStatisticsResponse.GroupStatistics> groupStatistics = new ArrayList<>();
            JSONArray groupsArray = result.getJSONArray("groups");
            for (int i = 0; i < groupsArray.size(); i++) {
                JSONObject groupObj = groupsArray.getJSONObject(i);
                CloudPodsServerSkuStatisticsResponse.GroupStatistics groupStats =
                        new CloudPodsServerSkuStatisticsResponse.GroupStatistics()
                                .setGroupField(groupObj.getString("group_field"))
                                .setGroupValue(groupObj.getString("group_value"))
                                .setGroupDisplayName(groupObj.getString("group_display_name"))
                                .setCount(groupObj.getInteger("count"))
                                .setPercentage(groupObj.getDouble("percentage"));
                groupStatistics.add(groupStats);
            }
            response.setGroupStatistics(groupStatistics);
        }
        
        return response;
    }
}
