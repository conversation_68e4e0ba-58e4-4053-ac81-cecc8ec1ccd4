package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.BillingRecordDao;
import com.cloudpod.podsail.db.entity.BillingRecord;
import com.cloudpod.podsail.db.mapper.BillingRecordMapper;
import com.cloudpod.podsail.dto.billing.BillingRecordCreateDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordQueryDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordResponseDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordUpdateDTO;
import com.cloudpod.podsail.service.BillingRecordService;
import com.cloudpod.podsail.service.UserServerInstanceService;
import com.cloudpod.podsail.service.app.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费记录服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class BillingRecordServiceImpl implements BillingRecordService {
    
    @Autowired
    private BillingRecordDao billingRecordDao;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserServerInstanceService userServerInstanceService;
    
    @Override
    public BillingRecordResponseDTO getBillingRecordById(Long id) {
        BillingRecord billingRecord = billingRecordDao.getById(id);
        return BeanCopyUtil.copyProperties(billingRecord, BillingRecordResponseDTO.class);
    }
    
    @Override
    public IPage<BillingRecordResponseDTO> getBillingRecordPage(BillingRecordQueryDTO queryDTO) {
        IPage<BillingRecord> page = queryDTO.page();
        QueryWrapper<BillingRecord> queryWrapper = buildBillingRecordQueryWrapper(queryDTO);
        
        IPage<BillingRecord> billingRecordPage = billingRecordDao.page(page, queryWrapper);
        
        // 转换为响应DTO
        List<BillingRecordResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
                billingRecordPage.getRecords(), BillingRecordResponseDTO.class);
        
        IPage<BillingRecordResponseDTO> responsePage = new Page<>(
                billingRecordPage.getCurrent(), billingRecordPage.getSize(), billingRecordPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }
    
   /**
     * 构建计费记录查询条件
     */
    private QueryWrapper<BillingRecord> buildBillingRecordQueryWrapper(BillingRecordQueryDTO queryDTO) {
        QueryWrapper<BillingRecord> queryWrapper = new QueryWrapper<>();
        
        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (queryDTO.getServerInstanceId() != null) {
            queryWrapper.eq("server_instance_id", queryDTO.getServerInstanceId());
        }
        if (queryDTO.getBillingType() != null) {
            queryWrapper.eq("billing_type", queryDTO.getBillingType());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq("billing_cycle", queryDTO.getBillingCycle());
        }
        if (queryDTO.getStartTimeStart() != null) {
            queryWrapper.ge("start_time", queryDTO.getStartTimeStart());
        }
        if (queryDTO.getStartTimeEnd() != null) {
            queryWrapper.le("start_time", queryDTO.getStartTimeEnd());
        }
        if (queryDTO.getEndTimeStart() != null) {
            queryWrapper.ge("end_time", queryDTO.getEndTimeStart());
        }
        if (queryDTO.getEndTimeEnd() != null) {
            queryWrapper.le("end_time", queryDTO.getEndTimeEnd());
        }
        if (queryDTO.getAmountMin() != null) {
            queryWrapper.ge("amount", queryDTO.getAmountMin());
        }
        if (queryDTO.getAmountMax() != null) {
            queryWrapper.le("amount", queryDTO.getAmountMax());
        }
        if (StringUtils.hasText(queryDTO.getCurrency())) {
            queryWrapper.eq("currency", queryDTO.getCurrency());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq("status", queryDTO.getStatus());
        }
        if (queryDTO.getChargeTimeStart() != null) {
            queryWrapper.ge("charge_time", queryDTO.getChargeTimeStart());
        }
        if (queryDTO.getChargeTimeEnd() != null) {
            queryWrapper.le("charge_time", queryDTO.getChargeTimeEnd());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq("remark", queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like("remark", queryDTO.getRemarkLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }
        
        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }
}
