package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserOrderDao;
import com.cloudpod.podsail.db.entity.UserOrder;
import com.cloudpod.podsail.db.mapper.UserOrderMapper;
import com.cloudpod.podsail.dto.order.UserOrderCreateDTO;
import com.cloudpod.podsail.dto.order.UserOrderQueryDTO;
import com.cloudpod.podsail.dto.order.UserOrderResponseDTO;
import com.cloudpod.podsail.dto.order.UserOrderUpdateDTO;
import com.cloudpod.podsail.service.UserOrderService;
import com.cloudpod.podsail.service.app.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户订单服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class UserOrderServiceImpl   implements UserOrderService {

    @Autowired
    private UserOrderDao userOrderDao;

    @Autowired
    private UserService userService;

    private static final AtomicLong ORDER_SEQUENCE = new AtomicLong(1);

    @Override
    public UserOrderResponseDTO getUserOrderById(Long id) {
        UserOrder userOrder = userOrderDao.getById(id);
        return BeanCopyUtil.copyProperties(userOrder, UserOrderResponseDTO.class);
    }

    @Override
    public IPage<UserOrderResponseDTO> getUserOrderPage(UserOrderQueryDTO queryDTO) {
        IPage<UserOrder> page = queryDTO.page();
        QueryWrapper<UserOrder> queryWrapper = buildUserOrderQueryWrapper(queryDTO);
        
        IPage<UserOrder> userOrderPage = userOrderDao.page(page, queryWrapper);
        
        // 转换为响应DTO
        List<UserOrderResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            userOrderPage.getRecords(), UserOrderResponseDTO.class);
        
        IPage<UserOrderResponseDTO> responsePage = new Page<>(
            userOrderPage.getCurrent(), userOrderPage.getSize(), userOrderPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    /**
     * 构建用户订单查询条件
     */
    private QueryWrapper<UserOrder> buildUserOrderQueryWrapper(UserOrderQueryDTO queryDTO) {
        QueryWrapper<UserOrder> queryWrapper = new QueryWrapper<>();

        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (StringUtils.hasText(queryDTO.getOrderNo())) {
            queryWrapper.eq("order_no", queryDTO.getOrderNo());
        }
        if (StringUtils.hasText(queryDTO.getOrderNoLike())) {
            queryWrapper.like("order_no", queryDTO.getOrderNoLike());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuId())) {
            queryWrapper.eq("server_sku_id", queryDTO.getServerSkuId());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuIdLike())) {
            queryWrapper.like("server_sku_id", queryDTO.getServerSkuIdLike());
        }
        if (queryDTO.getBillingMethodId() != null) {
            queryWrapper.eq("billing_method_id", queryDTO.getBillingMethodId());
        }
        if (queryDTO.getBillingType() != null) {
            queryWrapper.eq("billing_type", queryDTO.getBillingType());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq("billing_cycle", queryDTO.getBillingCycle());
        }
        if (queryDTO.getQuantityMin() != null) {
            queryWrapper.ge("quantity", queryDTO.getQuantityMin());
        }
        if (queryDTO.getQuantityMax() != null) {
            queryWrapper.le("quantity", queryDTO.getQuantityMax());
        }
        if (queryDTO.getUnitPriceMin() != null) {
            queryWrapper.ge("unit_price", queryDTO.getUnitPriceMin());
        }
        if (queryDTO.getUnitPriceMax() != null) {
            queryWrapper.le("unit_price", queryDTO.getUnitPriceMax());
        }
        if (queryDTO.getTotalAmountMin() != null) {
            queryWrapper.ge("total_amount", queryDTO.getTotalAmountMin());
        }
        if (queryDTO.getTotalAmountMax() != null) {
            queryWrapper.le("total_amount", queryDTO.getTotalAmountMax());
        }
        if (queryDTO.getActualAmountMin() != null) {
            queryWrapper.ge("actual_amount", queryDTO.getActualAmountMin());
        }
        if (queryDTO.getActualAmountMax() != null) {
            queryWrapper.le("actual_amount", queryDTO.getActualAmountMax());
        }
        if (StringUtils.hasText(queryDTO.getCurrency())) {
            queryWrapper.eq("currency", queryDTO.getCurrency());
        }
        if (queryDTO.getOrderStatus() != null) {
            queryWrapper.eq("order_status", queryDTO.getOrderStatus());
        }
        if (queryDTO.getPayStatus() != null) {
            queryWrapper.eq("pay_status", queryDTO.getPayStatus());
        }
        if (queryDTO.getPayTimeStart() != null) {
            queryWrapper.ge("pay_time", queryDTO.getPayTimeStart());
        }
        if (queryDTO.getPayTimeEnd() != null) {
            queryWrapper.le("pay_time", queryDTO.getPayTimeEnd());
        }
        if (queryDTO.getExpireTimeStart() != null) {
            queryWrapper.ge("expire_time", queryDTO.getExpireTimeStart());
        }
        if (queryDTO.getExpireTimeEnd() != null) {
            queryWrapper.le("expire_time", queryDTO.getExpireTimeEnd());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq("remark", queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like("remark", queryDTO.getRemarkLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }

        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }

}
