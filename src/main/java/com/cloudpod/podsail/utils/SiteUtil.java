package com.cloudpod.podsail.utils;

import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;

/**
 *
 * @className SiteUtil
 * <AUTHOR>
 * @date 2025/8/22 21:35
 * @description: TODO 
 */
public class SiteUtil {

    /**
     * 1-国际 2-国内
     */
    static Integer SITE_INTERNATIONAL = 1;
    static Integer SITE_INTERNAL = 2;

    /**
     * 获取当前的站点信息
     *
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2025/8/22 21:35
     */
    public static Integer getSite() {
        String site = System.getenv("site");
        if (StrUtil.isEmpty(site)) {
            return SITE_INTERNATIONAL;
        }
        return Integer.parseInt(site);
    }
}
