spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: YUj5ePccyIoUCrUF
  redis:
    redisson:
      config: |
        singleServerConfig:
          address: "redis://home.2ops.top:32379"  
          password: "YUj5ePccyIoUCrUF"          
          timeout: 3000                     
          retryAttempts: 3                  
          retryInterval: 1500               
          subscriptionsPerConnection: 5   
          clientName: "my-redisson-client" 
          database: 0                      

  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: norep<PERSON>@daddylab.com
    password: aSwLgaTnr5FnQeDo
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: smtp.exmail.qq.com
            enable: false
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory


cloudpods:
  client:
    authUrl: https://*************:30001/api/s/identity/v3
    user: admin
    password: admin@123
    project: system
    projectDomain: Default
    userDomain: Default
logging:
  level:
    root: info
    com:
      cloudpod:
        podsail: debug





