package com.cloudpod.podsail;

import com.cloudpod.podsail.service.cloudpods.CloudPodsService;
import com.cloudpod.podsail.service.cloudpods.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CloudPods集成测试
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@SpringBootTest
public class CloudPodsIntegrationTest {

    @Autowired
    private CloudPodsService cloudPodsService;

    @Test
    public void testCloudPodsConnection() {
        log.info("测试CloudPods连接");
        boolean isConnected = cloudPodsService.checkConnection();
        assertTrue(isConnected, "CloudPods连接应该成功");
    }


    @Test
    public void testGetVirtualMachineList() {
        log.info("测试查询虚拟机列表");
        CloudPodsVmQueryRequest queryRequest = new CloudPodsVmQueryRequest()
                .setHypervisor("kvm")
                .setLimit(10);

        CloudPodsVmListResponse response = cloudPodsService.getVirtualMachineList(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        
        log.info("查询到虚拟机数量: {}", response.getTotal());
        if (response.getServers() != null && !response.getServers().isEmpty()) {
            response.getServers().forEach(server -> 
                log.info("虚拟机: id={}, name={}, status={}, sku={}", 
                    server.getId(), server.getName(), server.getStatus(), server.getSku())
            );
        }
    }
}
