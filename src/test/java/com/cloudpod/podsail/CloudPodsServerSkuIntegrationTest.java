package com.cloudpod.podsail;

import com.cloudpod.podsail.service.cloudpods.serversku.CloudPodsServerSkuService;
import com.cloudpod.podsail.service.cloudpods.serversku.dto.*;
import com.cloudpod.podsail.util.CloudPodsTestUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CloudPods ServerSku 集成测试
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Slf4j
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CloudPodsServerSkuIntegrationTest {
    
    @Autowired
    private CloudPodsServerSkuService cloudPodsServerSkuService;
    
    private static String testServerSkuId;
    private static final String TEST_SKU_NAME = "test-sku-" + System.currentTimeMillis();
    
    @BeforeAll
    public static void beforeAll() {
        log.info("=== 集成测试开始 ===");
        CloudPodsTestUtils.enableMcClientDebugLogging();
    }
    
    @Test
    @Order(1)
    public void testGetServerSkuList() {
        log.info("=== 测试获取ServerSku列表 ===");
        
        CloudPodsServerSkuQueryRequest queryRequest = new CloudPodsServerSkuQueryRequest()
                .setLimit(10)
                .setEnabled("true");
        
        CloudPodsServerSkuListResponse response = cloudPodsServerSkuService.getServerSkuList(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        
        log.info("查询到ServerSku数量: {}", response.getTotal());
        if (response.getServerSkus() != null && !response.getServerSkus().isEmpty()) {
            response.getServerSkus().forEach(serverSku ->
                    log.info("ServerSku: {}", serverSku)
            );
            
            // 保存第一个ServerSku的ID用于后续测试
            if (testServerSkuId == null) {
                testServerSkuId = response.getServerSkus().get(0).getId();
                log.info("保存测试用ServerSku ID: {}", testServerSkuId);
            }
        }
    }
    
    @Test
    @Order(2)
    public void testGetServerSkuDetail() {
        log.info("=== 测试获取ServerSku详情 ===");
        
        if (testServerSkuId == null) {
            log.warn("跳过详情测试，因为没有可用的ServerSku ID");
            return;
        }
        
        CloudPodsServerSkuDetailResponse response = cloudPodsServerSkuService.getServerSkuDetail(testServerSkuId);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        assertNotNull(response.getServerSku(), "ServerSku详情不应为空");
        
        CloudPodsServerSkuDetailResponse.ServerSkuDetail serverSku = response.getServerSku();
        log.info("ServerSku详情: id={}, name={}, description={}, cpu={}, memory={}MB",
                serverSku.getId(), serverSku.getName(), serverSku.getDescription(),
                serverSku.getCpuCoreCount(), serverSku.getMemorySizeMb());
        log.info("提供商: {}, 云区域: {}, 可用区: {}",
                serverSku.getProvider(), serverSku.getCloudregion(), serverSku.getZone());
        log.info("状态: {}, 启用: {}, 公有云: {}",
                serverSku.getStatus(), serverSku.getEnabled(), serverSku.getPublicCloud());
    }
    
    @Test
    @Order(3)
    public void testGetInstanceSpecs() {
        log.info("=== 测试获取实例规格配置 ===");
        
        CloudPodsServerSkuInstanceSpecsQueryRequest queryRequest = new CloudPodsServerSkuInstanceSpecsQueryRequest()
                .setUsable(true)
                .setEnabled(true)
                .setCloudregion("default")
                .setProvider("OneCloud");
        
        CloudPodsServerSkuInstanceSpecsResponse response = cloudPodsServerSkuService.getInstanceSpecs(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        
        if (response.getCpuSpecs() != null) {
            log.info("可用CPU规格: {}", response.getCpuSpecs());
        }
        
        if (response.getMemorySpecs() != null) {
            log.info("可用内存规格: {}", response.getMemorySpecs());
        }
        
        if (response.getInstanceSpecs() != null && !response.getInstanceSpecs().isEmpty()) {
            log.info("实例规格组合数量: {}", response.getInstanceSpecs().size());
            response.getInstanceSpecs().stream().limit(5).forEach(spec ->
                    log.info("规格组合: CPU={}核, 内存={}MB, 分类={}, 类型={}, 可用={}",
                            spec.getCpuCoreCount(), spec.getMemorySizeMb(),
                            spec.getLocalCategory(), spec.getInstanceType(), spec.getAvailable())
            );
        }
        
        if (response.getStorageTypes() != null) {
            log.info("存储类型数量: {}", response.getStorageTypes().size());
            response.getStorageTypes().forEach((key, storageType) ->
                    log.info("存储类型: {}, 总容量: {}, 可用容量: {}",
                            key, storageType.getTotalCapacity(), storageType.getAvailableCapacity())
            );
        }
    }
    
    @Test
    @Order(4)
    public void testGetDistinctField() {
        log.info("=== 测试获取字段去重值 ===");
        
        // 测试获取本地分类的去重值
        CloudPodsServerSkuDistinctFieldQueryRequest queryRequest = new CloudPodsServerSkuDistinctFieldQueryRequest()
                .setField("local_category")
                .setPublicCloud(false)
                .setCloudregion("default")
                .setProvider("OneCloud")
                .setScope("project");
        
        CloudPodsServerSkuDistinctFieldResponse response = cloudPodsServerSkuService.getDistinctField(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        assertEquals("local_category", response.getField(), "字段名应该匹配");
        
        if (response.getValues() != null) {
            log.info("local_category字段的去重值: {}", response.getValues());
            log.info("去重值数量: {}", response.getTotal());
        }
        
        if (response.getValueInfos() != null) {
            response.getValueInfos().forEach(valueInfo ->
                    log.info("字段值详情: value={}, displayName={}, available={}",
                            valueInfo.getValue(), valueInfo.getDisplayName(), valueInfo.getAvailable())
            );
        }
    }
    
    @Test
    @Order(5)
    public void testGetStatistics() {
        log.info("=== 测试获取统计信息 ===");
        
        CloudPodsServerSkuStatisticsQueryRequest queryRequest = new CloudPodsServerSkuStatisticsQueryRequest()
                .setCloudregion("default")
                .setProvider("OneCloud")
                .setEnabled(true)
                .setStatisticsType("count")
                .setGroupBy("local_category");
        
        CloudPodsServerSkuStatisticsResponse response = cloudPodsServerSkuService.getStatistics(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        
        if (response.getOverallStatistics() != null) {
            CloudPodsServerSkuStatisticsResponse.OverallStatistics overall = response.getOverallStatistics();
            log.info("总体统计: 总数={}, 启用={}, 禁用={}, 可用={}, 不可用={}",
                    overall.getTotalCount(), overall.getEnabledCount(), overall.getDisabledCount(),
                    overall.getAvailableCount(), overall.getUnavailableCount());
            log.info("云类型统计: 公有云={}, 私有云={}",
                    overall.getPublicCloudCount(), overall.getPrivateCloudCount());
            log.info("资源统计: 总CPU核数={}, 总内存={}MB, 平均CPU={}, 平均内存={}MB",
                    overall.getTotalCpuCores(), overall.getTotalMemoryMb(),
                    overall.getAverageCpuCores(), overall.getAverageMemoryMb());
        }
        
        if (response.getGroupStatistics() != null && !response.getGroupStatistics().isEmpty()) {
            log.info("分组统计数量: {}", response.getGroupStatistics().size());
            response.getGroupStatistics().forEach(groupStats ->
                    log.info("分组统计: {}={}, 数量={}, 百分比={}%",
                            groupStats.getGroupField(), groupStats.getGroupValue(),
                            groupStats.getCount(), groupStats.getPercentage())
            );
        }
    }
    
    @Test
    @Order(6)
    public void testCreateServerSku() {
        log.info("=== 测试创建ServerSku ===");
        
        Map<String, String> tags = new HashMap<>();
        tags.put("environment", "test");
        tags.put("created_by", "integration_test");
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("test_purpose", "integration_testing");
        metadata.put("auto_cleanup", true);
        
        CloudPodsServerSkuCreateRequest createRequest = new CloudPodsServerSkuCreateRequest()
                .setName(TEST_SKU_NAME)
                .setDescription("Integration test ServerSku")
                .setCpuCoreCount(2)
                .setMemorySizeMb(4096)
                .setLocalCategory("general_purpose")
                .setInstanceTypeFamily("general")
                .setInstanceType("g1.medium")
                .setProvider("OneCloud")
                .setEnabled(true)
                .setPublicCloud(false)
                .setPostpaidStatus("available")
                .setHypervisor("kvm")
                .setOsArch("x86_64")
                .setTags(tags)
                .setMetadata(metadata);
        
        CloudPodsServerSkuCreateResponse response = cloudPodsServerSkuService.createServerSku(createRequest);
        
        assertNotNull(response, "响应不应为空");
        
        if (response.getSuccess()) {
            assertNotNull(response.getServerSkuId(), "创建的ServerSku ID不应为空");
            assertEquals(TEST_SKU_NAME, response.getServerSkuName(), "ServerSku名称应该匹配");
            
            // 保存创建的ServerSku ID用于后续测试
            testServerSkuId = response.getServerSkuId();
            
            log.info("成功创建ServerSku: id={}, name={}",
                    response.getServerSkuId(), response.getServerSkuName());
        } else {
            log.warn("创建ServerSku失败: {}", response.getErrorMessage());
            // 创建失败不影响其他测试，可能是权限或配置问题
        }
    }
    
    @Test
    @Order(7)
    public void testUpdateServerSku() {
        log.info("=== 测试更新ServerSku ===");
        
        if (testServerSkuId == null) {
            log.warn("跳过更新测试，因为没有可用的ServerSku ID");
            return;
        }
        
        Map<String, String> updatedTags = new HashMap<>();
        updatedTags.put("environment", "test");
        updatedTags.put("updated_by", "integration_test");
        updatedTags.put("last_updated", String.valueOf(System.currentTimeMillis()));
        
        CloudPodsServerSkuUpdateRequest updateRequest = new CloudPodsServerSkuUpdateRequest()
                .setDescription("Updated integration test ServerSku")
                .setCpuCoreCount(4)
                .setMemorySizeMb(8192)
                .setTags(updatedTags);
        
        CloudPodsServerSkuUpdateResponse response =
                cloudPodsServerSkuService.updateServerSku(testServerSkuId, updateRequest);
        
        assertNotNull(response, "响应不应为空");
        
        if (response.getSuccess()) {
            assertEquals(testServerSkuId, response.getServerSkuId(), "ServerSku ID应该匹配");
            log.info("成功更新ServerSku: id={}, name={}",
                    response.getServerSkuId(), response.getServerSkuName());
        } else {
            log.warn("更新ServerSku失败: {}", response.getErrorMessage());
        }
    }
    
    @Test
    @Order(8)
    public void testEnableDisableServerSku() {
        log.info("=== 测试启用/禁用ServerSku ===");
        
        if (testServerSkuId == null) {
            log.warn("跳过启用/禁用测试，因为没有可用的ServerSku ID");
            return;
        }
        
        // 测试禁用
        CloudPodsServerSkuOperationResponse disableResponse =
                cloudPodsServerSkuService.disableServerSku(testServerSkuId);
        assertNotNull(disableResponse, "禁用响应不应为空");
        
        if (disableResponse.getSuccess()) {
            assertEquals("disable", disableResponse.getOperation(), "操作类型应该是disable");
            assertEquals(testServerSkuId, disableResponse.getServerSkuId(), "ServerSku ID应该匹配");
            log.info("成功禁用ServerSku: {}", testServerSkuId);
        } else {
            log.warn("禁用ServerSku失败: {}", disableResponse.getErrorMessage());
        }
        
        // 测试启用
        CloudPodsServerSkuOperationResponse enableResponse = cloudPodsServerSkuService.enableServerSku(testServerSkuId);
        assertNotNull(enableResponse, "启用响应不应为空");
        
        if (enableResponse.getSuccess()) {
            assertEquals("enable", enableResponse.getOperation(), "操作类型应该是enable");
            assertEquals(testServerSkuId, enableResponse.getServerSkuId(), "ServerSku ID应该匹配");
            log.info("成功启用ServerSku: {}", testServerSkuId);
        } else {
            log.warn("启用ServerSku失败: {}", enableResponse.getErrorMessage());
        }
    }
    
    @Test
    @Order(9)
    public void testDeleteServerSku() {
        log.info("=== 测试删除ServerSku ===");
        
        if (testServerSkuId == null) {
            log.warn("跳过删除测试，因为没有可用的测试ServerSku ID或ID不是测试创建的");
            return;
        }
        
        CloudPodsServerSkuDeleteResponse response = cloudPodsServerSkuService.deleteServerSku(testServerSkuId);
        
        assertNotNull(response, "响应不应为空");
        
        if (response.getSuccess()) {
            assertEquals(testServerSkuId, response.getServerSkuId(), "ServerSku ID应该匹配");
            log.info("成功删除ServerSku: id={}, name={}",
                    response.getServerSkuId(), response.getServerSkuName());
        } else {
            log.warn("删除ServerSku失败: {}", response.getErrorMessage());
        }
    }
    
    @Test
    @Order(10)
    public void testQueryWithFilters() {
        log.info("=== 测试带过滤条件的查询 ===");
        
        // 测试多种过滤条件的组合查询
        CloudPodsServerSkuQueryRequest queryRequest = new CloudPodsServerSkuQueryRequest()
                .setLimit(5)
                .setEnabled("true");
        
        CloudPodsServerSkuListResponse response = cloudPodsServerSkuService.getServerSkuList(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        
        log.info("过滤查询结果数量: {}", response.getTotal());
        if (response.getServerSkus() != null && !response.getServerSkus().isEmpty()) {
            response.getServerSkus().forEach(serverSku -> {
                log.info("过滤结果: id={}, name={}, cpu={}, memory={}MB, category={}, provider={}",
                        serverSku.getId(), serverSku.getName(), serverSku.getCpuCoreCount(),
                        serverSku.getMemorySizeMb(), serverSku.getLocalCategory(), serverSku.getProvider());
                
                // 验证过滤条件
                assertEquals(true, serverSku.getEnabled(), "应该只返回启用的ServerSku");
                assertEquals("OneCloud", serverSku.getProvider(), "提供商应该匹配");
                if (serverSku.getCpuCoreCount() != null) {
                    assertEquals(Integer.valueOf(2), serverSku.getCpuCoreCount(), "CPU核数应该匹配");
                }
                if (serverSku.getMemorySizeMb() != null) {
                    assertEquals(Integer.valueOf(4096), serverSku.getMemorySizeMb(), "内存大小应该匹配");
                }
            });
        }
    }
}
