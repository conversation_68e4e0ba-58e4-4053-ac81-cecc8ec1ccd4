package com.cloudpod.podsail.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;

@Slf4j
public class CloudPodsTestUtils {
    /**
     * 启用 mcclient 内部调试日志
     * 通过反射修改 mcclient 内部 logger 的级别
     */
    public static void enableMcClientDebugLogging() {
        try {
            // 获取所有可能的 mcclient logger 并设置为 DEBUG 级别
            String[] loggerNames = {
                    "com.yunionyun.mcp.mcclient.Client",
                    "com.yunionyun.mcp.mcclient.Session",
                    "com.yunionyun.mcp.mcclient.AuthAgent",
                    "com.yunionyun.mcp.mcclient.managers.BaseManager",
                    "com.yunionyun.mcp.mcclient.managers.ResourceManager",
                    "com.yunionyun.mcp.mcclient.managers.impl.compute.ServerManager"
            };
            
            for (String loggerName : loggerNames) {
                Logger logger = (Logger) LoggerFactory.getLogger(loggerName);
                logger.setLevel(Level.DEBUG);
                log.info("设置 {} 日志级别为 DEBUG", loggerName);
            }
            
            // 尝试通过反射修改 Client 类的静态 logger 字段
            try {
                Class<?> clientClass = Class.forName("com.yunionyun.mcp.mcclient.Client");
                Field loggerField = clientClass.getDeclaredField("logger");
                loggerField.setAccessible(true);
                
                Logger clientLogger = (Logger) loggerField.get(null);
                if (clientLogger != null) {
                    clientLogger.setLevel(Level.DEBUG);
                    log.info("通过反射设置 Client.logger 级别为 DEBUG");
                }
            } catch (Exception e) {
                log.warn("无法通过反射修改 Client.logger: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("启用 mcclient 调试日志失败", e);
        }
    }
}
