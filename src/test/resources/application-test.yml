# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据库配置 - 使用H2内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
        
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

# 日志配置
logging:
  level:
    com.cloudpod.podsail: DEBUG
    com.yunionyun.mcp: INFO
    org.springframework: INFO
    org.hibernate: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# CloudPods测试配置
cloudpods:
  test:
    # 是否启用真实的CloudPods连接测试
    enable-real-connection: false
    # 测试用的CloudPods配置
    auth:
      auth-url: "http://test-cloudpods:30357/v3"
      username: "test-admin"
      password: "test-password"
      project: "test-project"
      domain: "test-domain"
    # 测试超时配置
    timeout:
      connection: 5000
      read: 10000
    # 测试重试配置
    retry:
      max-attempts: 3
      delay: 1000

# 测试数据配置
test:
  data:
    # 是否自动清理测试数据
    auto-cleanup: true
    # 测试数据前缀
    prefix: "test-"
    # 测试ServerSku配置
    serversku:
      default-cpu: 2
      default-memory: 4096
      default-category: "general_purpose"
      default-provider: "OneCloud"
      default-region: "default"
      default-zone: "zone1"
      
# 测试环境特定配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
